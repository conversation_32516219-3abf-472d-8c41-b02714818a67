import * as React from 'react';
import { useState } from 'react';
import { Grid, Tabs } from 'zent';
import { omit, snakeCase } from 'lodash';
import { RouteProps } from 'react-router-dom';
import { PageBlock } from '@youzan/retail-components';
import { useFetchTable } from '@youzan/react-hooks';
import { sumByWidth } from '@youzan/retail-utils';
import { SellType } from '@youzan/zan-hasaki';
import { ShopTabs } from 'cpn/channel-tabs';
import { getAllChannelViewType, IsUnityModelUnifiedShop } from 'common/constants';
import { AllChannelUrl } from 'cpn/channel-tabs/constants';
import { saleStatusTabs, InnerSaleStatus, innerSaleStatusValueMap } from './const';
import Filter from './filter';
import { getColumns } from './columns';
import { Batch } from './batch';
import { IShopItemListDetailDTO } from '../types';
import {
  BatchModifySelectEnum,
  ListBatchContext,
  useBatchTableSelect
} from 'cpn/table-batch-way-select';
import { isHqStoreAndNewModel } from 'common/constants/data-permissions';

const { Container, Content, Header } = PageBlock;

const viewType = getAllChannelViewType();

const buildComboParams = (
  sellType: number,
  notExcludeAbilityMarks: number[] = [],
  includeAbilityMarks: number[] = []
) => {
  if (IsUnityModelUnifiedShop) {
    if (sellType === SellType.Combine) {
      return {
        notExcludeAbilityMarks: [...notExcludeAbilityMarks, 10033],
        includeAbilityMarks: [...includeAbilityMarks, 10033]
      };
    }
    if (sellType === SellType.Single) {
      return {};
    }
    return {
      notExcludeAbilityMarks: [...notExcludeAbilityMarks, 10033]
    };
  }
  return {};
};

const rowKey = 'uniqueKey'; // 为了兼容外卖渠道，需要额外设置rowKey

export const ShopList: React.FC<RouteProps> = () => {
  const [activeId, setActiveId] = useState(InnerSaleStatus.OnSale);
  const [selectedItemIds, setSelectedItemIds] = useState<string[]>([]);
  const {
    currentModify,
    setCurrentModify,
    inverseSelectData,
    setInverseSelectData
  } = useBatchTableSelect();

  const { items, filterProps, tableProps, reload } = useFetchTable(
    'youzan.item.lite.items.search/1.0.0',
    {
      shouldInitFetch: false,
      params: {
        viewType,
        activeId
      },
      handleParams: (value: any) => {
        const params: IObject = {
          ...omit(value, ['pageNo', 'sortBy', 'sortType', 'comboSellType', 'activeId']),
          ...buildComboParams(
            value.comboSellType,
            value.notExcludeAbilityMarks,
            value.includeAbilityMarks
          ),
          ...innerSaleStatusValueMap.get(value.activeId),
          page: value.pageNo,
          order: value.sortType,
          orderBy: value.sortBy ? snakeCase(value.sortBy) : null
        };

        return params;
      }
    }
  );

  const clearSelected = () => {
    setSelectedItemIds([]);
  };

  const onBatchOperationSuccess = () => {
    clearSelected();
    reload();
  };

  const handleTabsChange = (value: InnerSaleStatus) => {
    setActiveId(value);
    clearSelected();
  };

  const handleSelectionProps = (data: IShopItemListDetailDTO) => {
    // 禁用掉有时段库存的商品
    const { hasPeriodStockNum } = data;
    return {
      disabled: hasPeriodStockNum,
      reason: '活动预订商品不支持操作'
    };
  };

  const columns = getColumns({ reload, activeId });
  const scrollX = { x: sumByWidth(columns) };

  const datasets = items.map((item: any) => {
    const { itemId, channelId } = item;
    return {
      ...item,
      uniqueKey: `${channelId}_${itemId}`
    };
  });

  const dataFilterDisabled = React.useMemo(() => {
    return datasets.filter((item: IShopItemListDetailDTO) => !item.hasPeriodStockNum);
  }, [datasets]);

  const onSelect = (_selected: string[]) => {
    setSelectedItemIds(_selected);
    if (isHqStoreAndNewModel) {
      if (currentModify === BatchModifySelectEnum.MODIFY_ALL_GOODS) {
        const selectedIds = _selected.map(item => Number(item.split('_')[1]));
        // 获取当前页除了禁用的itemIds
        const curPageItemIds = dataFilterDisabled.map(item => item.itemId);
        // 获取当前页选中的itemIds
        const curPageSelectItemIds = selectedIds.filter(itemId => curPageItemIds.includes(itemId));
        const inverseItemIdData = dataFilterDisabled
          .filter(item => !curPageSelectItemIds.includes(item.itemId))
          .map(item => item.itemId);

        // 将其他页未选中的数据和当前页未选中的数据合并
        setInverseSelectData([
          ...inverseSelectData.current.filter(itemId => !curPageItemIds.includes(itemId)),
          ...inverseItemIdData
        ]);
      }
    }
  };

  console.log('tableProps', tableProps);

  return (
    <ListBatchContext.Provider
      value={{
        total: tableProps.pageInfo.total,
        onModifyWayChange: (key: BatchModifySelectEnum) => {
          setCurrentModify(key);
          clearSelected();
        }
      }}
    >
      <Container>
        <Content>
          <ShopTabs channelActiveId={AllChannelUrl} showChannelTab />
          <Header>
            <Filter {...filterProps} viewType={viewType} clearSelected={clearSelected} />
          </Header>
          <Content>
            <Tabs
              className="intro-tabs-scroll"
              type="card"
              tabs={saleStatusTabs}
              activeId={activeId}
              onChange={handleTabsChange}
            />
            <Grid
              rowKey={rowKey}
              {...tableProps}
              datasets={datasets}
              columns={columns}
              selection={{
                selectedRowKeys: selectedItemIds,
                onSelect,
                getSelectionProps: handleSelectionProps
              }}
              batchRender={(goodsList?: IShopItemListDetailDTO[]) => (
                <Batch
                  goodsList={goodsList}
                  onBatchOperationSuccess={onBatchOperationSuccess}
                  viewType={viewType}
                  enableTakeUp={[InnerSaleStatus.Total, InnerSaleStatus.OffSale].includes(activeId)}
                  enableTaskDown={[
                    InnerSaleStatus.Total,
                    InnerSaleStatus.OnSale,
                    InnerSaleStatus.SoldOut
                  ].includes(activeId)}
                />
              )}
              scroll={scrollX}
            />
          </Content>
        </Content>
      </Container>
    </ListBatchContext.Provider>
  );
};
