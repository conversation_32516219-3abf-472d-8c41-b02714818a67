import * as React from 'react';
import { FC } from 'react';
import { Space } from '@youzan/react-components';

import { ViewType } from 'common/constants';
import BatchChannelStock from 'cpn/batch-channel-stock';
import { BatchOperateType } from 'common/types';
import { Shelf, ShelfType } from './shelf';
import { PriceDialog } from './price';
import { trackOperateGoods } from './log';
import { isHqStoreAndNewModel } from 'common/constants/data-permissions';
import TableBatchWaySelect from 'cpn/table-batch-way-select';

interface IProps {
  goodsList?: any[];
  onBatchOperationSuccess?: () => void;
  viewType: ViewType;
  enableTakeUp: boolean;
  enableTaskDown: boolean;
}

export const Batch: FC<IProps> = ({
  goodsList = [],
  onBatchOperationSuccess,
  viewType,
  enableTakeUp,
  enableTaskDown
}) => {
  const itemIds = goodsList.map(goods => goods?.itemId) || [];

  const shelfItems: ShelfType[] = [];
  enableTakeUp && shelfItems.push(ShelfType.On);
  enableTaskDown && shelfItems.push(ShelfType.Off);

  const onBatchChannelOperationSuccess = () => {
    onBatchOperationSuccess?.();
    trackOperateGoods(BatchOperateType.Stock);
  };
  return (
    <div className="sss">
      <Space size="small">
        {isHqStoreAndNewModel ? (
          <TableBatchWaySelect />
        ) : (
          <>
            <p>当页全选</p>
            {goodsList?.length > 0 && <p className="has-selected">已选商品 {goodsList.length}</p>}
          </>
        )}
        {shelfItems.map(type => (
          <Shelf
            key={type}
            type={type}
            itemIds={itemIds}
            viewType={viewType}
            goodsList={goodsList}
            onConfirmSuccess={onBatchOperationSuccess}
          />
        ))}
        <PriceDialog goodsList={goodsList} onConfirmSuccess={onBatchOperationSuccess} />
        <BatchChannelStock
          goodsList={goodsList}
          onConfirmSuccess={onBatchChannelOperationSuccess}
        />
      </Space>
    </div>
  );
};
