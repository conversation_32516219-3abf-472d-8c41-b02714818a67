import React from 'react';
import {
  isRetailShop,
  isPartnerStore,
  isBranchStore,
  isSingleStore,
  isUnifiedHqStore,
  isPureWscSingleStore,
  isWscSingleStore,
  isUnifiedOfflineBranchStore,
  checkAbilityValid,
  ShopAbility
} from '@youzan/utils-shop';
import { get } from 'lodash';

import {
  HasSupplyChainAbility,
  IsUnityModelWithoutFwh,
  IsRetailMinimalistOrEduV4,
  IsRetailMinimalistOrEduV4Hq,
  StaffPermission,
  isOfflineBranchStore,
  HasMultiChannelProductManageAbility,
  IsUnityModel
} from 'common/constants';

import { global } from '@youzan/retail-utils';
import {
  ChainDBrandClassify,
  IsUnityModelUnifiedShop,
  IsLiteOnlineStoreManager
} from 'common/constants';
import { canIUse } from '@youzan/matrix-react-sdk';
import { ProductType, batchCodeText, FieldProductType } from '../constant';
import {
  isOfflineGroupSync,
  isPriceIndependent,
  isGoodsTitleIndependent,
  isGoodsDetailIndependent,
  isDeliveryIndependent,
  isNewCategory,
  expressDeliveryAbility,
  isInformPickUpFoodOn,
  isInLogisticsTimelinessWhitelist
} from './constant';
import CategoryUpgrade from './components/category-upgrade';

const { new_schedule_take_up_down_switch: newScheduleTakeUpDownSwitch } = global.BUSINESS;

const { BUSINESS } = global;
const { staffPermission } = BUSINESS;

/**
 * 弱管控模式下是否可以展示某字段，兼容了老商品模型、单店等场景
 * @param {*} fieldName
 * @returns
 */
function checkFieldsIsHide(fieldName: string, conditions?: boolean) {
  if (IsUnityModelUnifiedShop && (isBranchStore || IsLiteOnlineStoreManager)) {
    return !(
      get(BUSINESS, 'goodsWeakControlRules.canEdit', false) &&
      get(BUSINESS, 'goodsWeakControlRules.rules', []).some((rule: { fields: string[] }) =>
        rule.fields.includes(fieldName)
      )
    );
  }
  return conditions;
}

/* ---------------------------------- 商品库商品 START --------------------------------- */
// 商品库是否可售
export const STORAGE_GOODS_STATUS = 'shopItemGoodsStatus';
// 商品库商品分类
export const STORAGE_GOODS_CATEGORY = 'category';
// 商品库商品名称
export const STORAGE_GOODS_NAME = 'spuName';
// 商品库商品图片
export const STORAGE_PHOTO_URL = 'photoUrl';
// 首选供应商
export const GOODS_SUPPLIER = 'defaultVendor';
// 商品品牌
export const GOODS_BRAND = 'brand';
/** 店内组织 */
export const ONLINE_GOODS_ORG = 'onlineShopOrg';
// 生命周期
export const GOODS_LIFE_CYCLE = 'lifeCycle';
// 物流模式
export const DELIVERY_TYPE = 'deliveryType';
// 商品类目
export const STORAGE_CATEGORY = 'leafCategoryId';
// 商品类目描述
export const STORAGE_CATEGORY_DESC = 'leafCategoryIdDesc';
// 商品条码
export const STORAGE_GOODS_NO = 'updateSpuNo';
// 门店渠道，前端内部使用
export const OFFLINE_CHANNEL = 'offlineChannel';
// 网店渠道，前端内部使用
export const ONLINE_CHANNEL = 'onlineChannel';
// 门店和网店的渠道，提交给后端 统一用一个字段来表示
export const SPU_SELL_CHANNEL = 'spuSellChannel';
/** 加工时长 */
export const SPU_PRODUCTION_TIME = 'spuProductionTime';
/** 商品属性 */
export const GOODS_ATTR = 'goodsProps';
/** 高级版商品编码 */
export const STORAGE_GOODS_CODE = 'updateSpuCode';
/** 商品规格型号 */
export const STORAGE_SKU_PATTERN = 'specifications';
/** 进项税率 */
export const STORAGE_INPUT_TAX_RATE = 'inputTaxRate';
/** 销项税率 */
export const STORAGE_SALES_TAX_RATE = 'salesTaxRate';
/** 保质期管理 */
export const STORAGE_SHELF_LIFE_WARNING = 'shelfLifeWarning';
/** 分享描述 */
export const STORAGE_SHARE_DESC = 'shareDesc';
/** 商品卖点 */
export const SELL_POINT = 'sellPoint';

/* ---------------------------------- 商品库商品 END --------------------------------- */

/* ---------------------------------- 门店商品 START ---------------------------------- */

// 门店商品上架状态
export const OFFLINE_GOODS_STATUS = 'offlineGoodsStatus';
// 门店商品名称
export const OFFLINE_GOODS_NAME = 'offlineGoodsName';
// 门店商品会员折扣
export const OFFLINE_MEMBER_DISCOUNT = 'offlineMemberDiscount';
// 门店商品-商品分组
export const OFFLINE_GOODS_GROUP = 'offlineGoodsGroup';
// 门店商品定时上下架
export const OFFLINE_SCHEDULE_DISPLAY = 'offlineScheduleDisplay';
// 门店商品-限购
export const OFFLINE_BUY_LIMIT = 'offlineBuyLimitParam';

// 门店商品-起售数量
export const OFFLINE_START_SALE_NUM = 'offlineStartSaleNumParam';
/** 门店商品-商品关键标签 */
export const OFFLINE_GOODS_SPECIAL_LABEL = 'offlineGoodsSpecialLabel';
/** 门店商品-商品普通标签 */
export const OFFLINE_GOODS_NORMAL_LABEL = 'offlineGoodsNormalLabel';

/* ---------------------------------- 门店商品 END ---------------------------------- */

/* ------------------------------- 网店商品 START ------------------------------- */

// 网店商品状态
export const ONLINE_GOODS_STATUS = 'onlineGoodsStatus';
// 网店商品分组
export const ONLINE_GOODS_GROUP = 'onlineGoodsGroup';
// 商品页模板
export const ONLINE_GOODS_TEMPLATE = 'onlineGoodsTemplate';
// 网店商品名称
export const ONLINE_GOODS_NAME = 'onlineGoodsName';
// 网店会员折扣
export const ONLINE_MEMBER_DISCOUNT = 'onlineMemberDiscount';
// 开售时间
export const ONLINE_SALE_TIME = 'onlineSaleTime';
// 定时下架
export const SCHEDULE_DISPLAY_OFF = 'scheduleDisplayOff';
// 网店商品定时上下架
export const ONLINE_SCHEDULE_DISPLAY = 'onlineScheduleDisplay';
// 每人限购数
export const ONLINE_BUY_LIMIT = 'onlineBuyLimitParam';
// 身份限购
export const PURCHASE_RIGHT_REQUEST = 'purchaseRightRequest';
// 运费模板
export const ONLINE_DELIVERY_TEMPLATE = 'onlineDeliveryTemplate';
// 物流时效模板
export const ONLINE_LOGISTICS_TIMELINESS_TEMPLATE = 'onlineLogisticsTimelinessTemplate';
// 划线价
export const ONLINE_ORIGIN = 'onlineOrigin';
// 商品类目
export const ONLINE_CATEGORY = 'onlineCategoryId';
// 商品类目描述
export const ONLINE_CATEGORY_DESC = 'onlineCategoryIdDesc';
// 售后服务
export const ONLINE_AFTER_SALE = 'onlineAfterSale';
// 商品编码
export const ONLINE_GOODS_NO = 'onlineGoodsNo';
// 配送方式
export const ONLINE_DISTRIBUTION = 'onlineDistribution';
// 商品条码
export const ONLINE_BARCODE = 'onlineBarcode';
// 起售数量
export const ONLINE_START_SALE_NUM = 'startSaleNum';

// 售卖方式
export const ONLINE_SALE_TYPE = 'preSaleItemParam';
// 是否展示库存
export const ONLINE_SHOW_STOCK = 'onlineShowStock';
// 备货时间
export const ONLINE_PREPARE_TIME = 'onlinePrepareTime';
// 序号
export const ONLINE_GOODS_INDEX = 'goodsNum';
/** 加工时长 */
export const ONLINE_PRODUCTION_TIME = 'onlineProductionTime';
/** 连锁D商品分类 */
export const ONLINE_CLASSIFICATION = 'onlineClassification';
/** 连锁D商品品牌 */
export const ONLINE_BRAND = 'onlineBrand';
/** 续重收费 */
export const ONLINE_HEAVY_CONTINUED = 'onlineHeavyContinued';
/** 库存扣减方式 */
export const ONLINE_STOCK_DEDUCTION_MODE = 'onlineStockDeductionMode';
/** 商品关键标签 */
export const ONLINE_GOODS_SPECIAL_LABEL = 'onlineGoodsSpecialLabel';
/** 商品普通标签 */
export const ONLINE_GOODS_NORMAL_LABEL = 'onlineGoodsNormalLabel';
/** 商品留言 */
export const ONLINE_GOODS_COMMENT = 'onlineGoodsComment';

/* ------------------------------- 网店商品 END ------------------------------- */

/* ---------------------------------- 美团商品 START ---------------------------------- */

export const MEITUAN_GOODS_GROUP = 'meituanwaimaiGroup';
/** 美团外卖打包费 */
export const MEITUAN_PACKING_FEE = 'meituanPackingFee';

/* ------------------------------- 美团商品 END ------------------------------- */

/* ---------------------------------- 饿了么商品 START ---------------------------------- */

export const ELEME_GOODS_GROUP = 'elemeGroup';
/** 饿了么外卖打包费 */
export const ELEME_PACKING_FEE = 'elemePackingFee';

/* ------------------------------- 饿了么商品 END ------------------------------- */

/* ---------------------------------- 美团闪购商品 START ---------------------------------- */

export const MEITUAN_SHANGOU_GOODS_GROUP = 'meituanshangouGroup';
/** 美团闪购打包费 */
export const MEITUAN_SHANGOU_PACKING_FEE = 'meituanshangouPackingFee';

/* ------------------------------- 美团闪购商品 END ------------------------------- */

// 商品库商品fields
export const STORAGE_GOODS_FIELDS = [
  {
    text: '是否可售',
    value: STORAGE_GOODS_STATUS,
    isHide: !HasMultiChannelProductManageAbility
  },
  {
    text: '商品分类',
    value: STORAGE_GOODS_CATEGORY
  },
  {
    text: '商品名称',
    value: STORAGE_GOODS_NAME,
    helpDesc: IsUnityModelWithoutFwh ? '修改商品库的名称后，门店/网店的名称将同步修改' : null
  },
  {
    text: '首选供应商',
    value: GOODS_SUPPLIER,
    helpDesc: '采购补货时系统将自动输入商品的首选供应商',
    isHide: !HasSupplyChainAbility
  },
  {
    text: '商品品牌',
    value: GOODS_BRAND
  },
  {
    text: '生命周期',
    value: GOODS_LIFE_CYCLE
  },
  {
    text: '物流模式',
    value: DELIVERY_TYPE,
    helpDesc: '用于连锁门店要货物流模式',
    isHide: isSingleStore
  },
  {
    text: '可售渠道',
    value: SPU_SELL_CHANNEL,
    isHide: !isUnifiedHqStore || IsUnityModelWithoutFwh,
    helpDesc: '可售渠道仅支持在列表修改'
  },
  {
    text: '商品类目',
    value: STORAGE_CATEGORY,
    disabled: !isNewCategory,
    helpDesc: isNewCategory ? null : <CategoryUpgrade />
  },
  {
    text: '商品条码',
    value: STORAGE_GOODS_NO
  },
  {
    text: '商品编码',
    value: STORAGE_GOODS_CODE,
    isHide: !IsUnityModelWithoutFwh
  },
  {
    text: '加工时长',
    value: SPU_PRODUCTION_TIME,
    isHide: !isInformPickUpFoodOn
  },
  {
    text: '商品属性',
    value: GOODS_ATTR,
    isHide: !IsUnityModelWithoutFwh
  },
  {
    text: '规格型号',
    value: STORAGE_SKU_PATTERN,
    isHide: !IsUnityModelWithoutFwh
  },
  {
    text: '进项税率',
    value: STORAGE_INPUT_TAX_RATE,
  },
  {
    text: '销项税率',
    value: STORAGE_SALES_TAX_RATE,
  },
  {
    text: '保质期管理',
    value: STORAGE_SHELF_LIFE_WARNING,
  },
  {
    text: '分享描述',
    value: STORAGE_SHARE_DESC,
  },
  {
    text: '商品卖点',
    value: SELL_POINT,
    isHide: checkFieldsIsHide(
      'sellPoint',
      !(isPureWscSingleStore || IsUnityModel) || isPartnerStore
    ),
    isCreateHide: !isPureWscSingleStore,
    isSupportFenxiao: true
  },
].filter(field => !field.isHide);

// 门店商品fields
export const OFFLINE_GOODS_FIELDS = [
  {
    text: '商品状态',
    value: OFFLINE_GOODS_STATUS,
    helpDesc: IsUnityModel ? (
      <p>
        上架：操作上架默认全时段上架售卖，覆盖配置过的定时上下架规则。
        <br />
        下架：操作下架默认永久下架，覆盖配置过的定时上下架规则，除人为操作上架外保持下架状态。
      </p>
    ) : (
      '修改商品在门店的销售状态'
    ),
    disabled:
      isUnifiedOfflineBranchStore && !checkAbilityValid(ShopAbility.OfflineStoreGoodsManageAbility)
  },
  {
    text: '商品名称',
    value: OFFLINE_GOODS_NAME,
    isHide: checkFieldsIsHide('title', isOfflineBranchStore || isPartnerStore)
  },
  {
    text: '会员折扣',
    value: OFFLINE_MEMBER_DISCOUNT,
    isHide: isOfflineBranchStore || isPartnerStore
  },
  {
    text: '商品分组',
    value: OFFLINE_GOODS_GROUP,
    // 开启同步后不能编辑
    isHide: checkFieldsIsHide(
      'itemGroup',
      isPartnerStore || (IsUnityModelWithoutFwh && isOfflineBranchStore)
    ),
    disabled: isOfflineGroupSync,
    helpDesc: isOfflineGroupSync ? '当前已设置门店商品分组同步商品库分类，请直接修改商品分类' : null
  },
  {
    text: '定时上下架',
    value: OFFLINE_SCHEDULE_DISPLAY,
    helpDesc: (
      <p>
        1、 默认全时段上架售卖，可按需调整成其他选项；
        <br />
        2、下架时间需晚于开售时间，商品才能定时开售；
        <br />
        3、 套餐商品上架中，套餐下子商品定时下架不强制执行
      </p>
    ),
    isHide: !IsUnityModel || (isRetailShop && !!newScheduleTakeUpDownSwitch)
  },
  {
    text: '每人限购数',
    value: OFFLINE_BUY_LIMIT,
    isHide: checkFieldsIsHide('quota', isBranchStore || isPartnerStore)
  },
  {
    text: '身份限购',
    value: PURCHASE_RIGHT_REQUEST,
    isHide: isBranchStore || isPartnerStore
  },
  {
    text: '起售数量',
    value: OFFLINE_START_SALE_NUM,
    isHide: checkFieldsIsHide('startSaleNum', isBranchStore || isPartnerStore)
  },
  {
    text: '商品关键标签',
    value: OFFLINE_GOODS_SPECIAL_LABEL,
    isHide: checkFieldsIsHide('specialLabel', isBranchStore || isPartnerStore)
  },
  {
    text: '商品普通标签',
    value: OFFLINE_GOODS_NORMAL_LABEL,
    isHide: checkFieldsIsHide('normalLabel', isBranchStore || isPartnerStore)
  }
].filter(field => !field.isHide);

// 网店商品fields
export const ONLINE_All_GOODS_FIELDS = [
  {
    text: '商品状态',
    value: ONLINE_GOODS_STATUS,
    helpDesc: (() => {
      if (IsUnityModel) {
        return (
          <p>
            上架：操作上架默认全时段上架售卖，覆盖配置过的定时上下架规则。
            <br />
            下架：操作下架默认永久下架，覆盖配置过的定时上下架规则，除人为操作上架外保持下架状态。
          </p>
        );
      }
      if (isWscSingleStore) {
        return '修改商品的上下架状态';
      }
      return '修改商品在网店的销售状态';
    })()
  },
  {
    text: '商品分组',
    value: ONLINE_GOODS_GROUP,
    isHide: checkFieldsIsHide('itemGroup', isPartnerStore),
    isSupportFenxiao: true
  },
  {
    text: '商品页模板',
    value: ONLINE_GOODS_TEMPLATE,
    helpDesc: '设置用户端商品页模板',
    isHide: checkFieldsIsHide(
      'detail',
      (isBranchStore && !isGoodsDetailIndependent) || isPartnerStore
    ),
    isSupportFenxiao: true
  },
  {
    text: '商品名称',
    value: ONLINE_GOODS_NAME,
    isHide: checkFieldsIsHide(
      'title',
      (isBranchStore && !isGoodsTitleIndependent) || isPartnerStore
    ),
    isSupportFenxiao: true
  },
  {
    text: '会员折扣',
    value: ONLINE_MEMBER_DISCOUNT,
    isHide: isBranchStore || isPartnerStore,
    isSupportFenxiao: true
  },
  {
    text: '开售时间',
    value: ONLINE_SALE_TIME,
    isHide: isBranchStore || isPartnerStore
  },
  {
    text: '定时下架',
    value: SCHEDULE_DISPLAY_OFF,
    helpDesc: '开启后，系统会在设置时间下架该商品。下架时间需晚于开售时间，商品才能定时开售。',
    isHide:
      (!isWscSingleStore && !IsRetailMinimalistOrEduV4) ||
      (isRetailShop && !!newScheduleTakeUpDownSwitch),
    isCreateHide: !isWscSingleStore && !IsRetailMinimalistOrEduV4,
    isSupportFenxiao: true
  },
  {
    text: '每人限购数',
    value: ONLINE_BUY_LIMIT,
    isHide: checkFieldsIsHide('quota', isBranchStore || isPartnerStore)
  },
  {
    text: '身份限购',
    value: PURCHASE_RIGHT_REQUEST,
    isHide: isBranchStore || isPartnerStore
  },
  {
    text: '运费模板',
    value: ONLINE_DELIVERY_TEMPLATE,
    isHide: checkFieldsIsHide(
      'deliverySetting',
      (isBranchStore && !isDeliveryIndependent) || !expressDeliveryAbility
    )
  },
  {
    text: '物流时效模板',
    value: ONLINE_LOGISTICS_TIMELINESS_TEMPLATE,
    isHide: !isInLogisticsTimelinessWhitelist || !isWscSingleStore,
    isCreateHide: !isInLogisticsTimelinessWhitelist || !isWscSingleStore
  },
  {
    text: '划线价',
    value: ONLINE_ORIGIN,
    isHide: checkFieldsIsHide('price', (isBranchStore && !isPriceIndependent) || isPartnerStore),
    isSupportFenxiao: true
  },
  {
    text: '商品类目',
    value: ONLINE_CATEGORY,
    isHide: !IsRetailMinimalistOrEduV4Hq,
    disabled: !isNewCategory,
    helpDesc: isNewCategory ? null : <CategoryUpgrade />
  },
  {
    text: '售后服务',
    value: ONLINE_AFTER_SALE,
    isHide: isBranchStore || isPartnerStore
  },
  {
    text: `商品${batchCodeText}`,
    value: ONLINE_GOODS_NO,
    isHide: IsUnityModelWithoutFwh || isBranchStore || isPartnerStore
  },
  {
    text: '商品条码',
    value: ONLINE_BARCODE,
    isHide:
      IsUnityModelWithoutFwh ||
      (!IsRetailMinimalistOrEduV4 && !IsUnityModelWithoutFwh) ||
      isBranchStore ||
      isPartnerStore,
    isCreateHide: (!IsRetailMinimalistOrEduV4 || isPartnerStore) && !isPureWscSingleStore
  },
  {
    text: '配送方式',
    value: ONLINE_DISTRIBUTION,
    isHide: checkFieldsIsHide('deliverySetting', isBranchStore || isPartnerStore)
  },
  {
    text: '起售数量',
    value: ONLINE_START_SALE_NUM,
    isHide: checkFieldsIsHide('startSaleNum', isBranchStore || isPartnerStore)
  },
  {
    text: '售卖方式',
    value: ONLINE_SALE_TYPE,
    isHide: checkFieldsIsHide('preSale', isBranchStore || isPartnerStore)
  },
  {
    text: '是否展示库存',
    value: ONLINE_SHOW_STOCK,
    helpDesc: '在商品详情和购物车页面设置是否展示商品的剩余件数',
    isHide: isBranchStore || isPartnerStore,
    isSupportFenxiao: true
  },
  {
    text: '备货时间',
    value: ONLINE_PREPARE_TIME,
    helpDesc: '若商品为多规格商品，设置备货时间后，商品下所有规格的备货时间都相同',
    isHide: checkFieldsIsHide('deliverySetting', isBranchStore || isPartnerStore)
  },
  {
    text: '序号',
    value: ONLINE_GOODS_INDEX,
    isHide: !(IsUnityModelWithoutFwh || isWscSingleStore) || isBranchStore || isPartnerStore
  },
  {
    text: '店内组织',
    value: ONLINE_GOODS_ORG,
    helpDesc: '仅实物商品和海淘商品支持店内组织，其他商品类型不支持设置',
    isHide: !canIUse('goods.edit.setup-org') || staffPermission === StaffPermission.Deny, // 无店铺能力 或 无权限
    isCreateHide: !canIUse('goods.edit.setup-org') || staffPermission === StaffPermission.Deny // 无店铺能力 或 无权限
  },
  {
    text: '商品分类',
    value: ONLINE_CLASSIFICATION,
    isHide: !ChainDBrandClassify || isBranchStore || isPartnerStore,
    isCreateHide: !ChainDBrandClassify
  },
  {
    text: '商品品牌',
    value: ONLINE_BRAND,
    isHide: !ChainDBrandClassify || isBranchStore || isPartnerStore,
    isCreateHide: !ChainDBrandClassify
  },
  {
    text: '续重收费',
    value: ONLINE_HEAVY_CONTINUED,
    helpDesc: '续重收费开启前提是【商品重量】有值且【配送方式】含同城配送，否则无法开启',
    isHide: checkFieldsIsHide(
      'deliverySetting',
      isBranchStore || isPartnerStore || !IsUnityModelWithoutFwh
    ),
    isCreateHide: isWscSingleStore
  },
  {
    text: '定时上下架',
    value: ONLINE_SCHEDULE_DISPLAY,
    helpDesc: (
      <p>
        1、 默认全时段上架售卖，可按需调整成其他选项；
        <br />
        2、下架时间需晚于开售时间，商品才能定时开售；
        <br />
        3、 套餐商品上架中，套餐下子商品定时下架不强制执行
      </p>
    ),
    isHide: !IsUnityModel || (isRetailShop && !!newScheduleTakeUpDownSwitch),
    isCreateHide: !IsUnityModel,
    isSupportFenxiao: true
  },
  {
    text: '库存扣减方式',
    value: ONLINE_STOCK_DEDUCTION_MODE,
    isHide: !(IsUnityModelWithoutFwh || isWscSingleStore) || isBranchStore || isPartnerStore,
  },
  {
    text: '商品关键标签',
    value: ONLINE_GOODS_SPECIAL_LABEL,
    isHide: checkFieldsIsHide('specialLabel', isBranchStore || isPartnerStore),
  },
  {
    text: '商品普通标签',
    value: ONLINE_GOODS_NORMAL_LABEL,
    isHide: checkFieldsIsHide('normalLabel', isBranchStore || isPartnerStore),
  },
  {
    text: '商品留言',
    value: ONLINE_GOODS_COMMENT,
  }
];

export const MEITUAN_GOODS_FIELDS = [
  {
    text: '商品分组',
    value: MEITUAN_GOODS_GROUP
  },
  {
    text: '打包费',
    value: MEITUAN_PACKING_FEE,
  }
];

export const ELEME_GOODS_FIELDS = [
  {
    text: '商品分组',
    value: ELEME_GOODS_GROUP
  },
  {
    text: '打包费',
    value: ELEME_PACKING_FEE,
  }
];

export const MEITUAN_SHANGOU_GOODS_FIELDS = [
  {
    text: '商品分组',
    value: MEITUAN_SHANGOU_GOODS_GROUP
  },
  {
    text: '打包费',
    value: MEITUAN_SHANGOU_PACKING_FEE,
  }
];

// 网店非自建商品fields
export const ONLINE_GOODS_FIELDS = ONLINE_All_GOODS_FIELDS.filter(field => !field.isHide);
// 网店自建商品fields
export const ONLINE_CREATE_GOODS_FIELDS = ONLINE_All_GOODS_FIELDS.filter(
  field => !field.isCreateHide
);
/**
 * 网店分销商品fields
 */
export const ONLINE_FENXIAO_FIELDS = ONLINE_All_GOODS_FIELDS.filter(
  field => field.isSupportFenxiao
);

export const GOODS_FIELDS_MAP: Record<
  FieldProductType,
  {
    text: string;
    value: string;
    helpDesc?: React.ReactNode;
    isHide?: boolean;
    isCreateHide?: boolean;
  }[]
> = {
  [ProductType.Storage]: STORAGE_GOODS_FIELDS,
  [ProductType.Offline]: OFFLINE_GOODS_FIELDS,
  [ProductType.Online]: ONLINE_GOODS_FIELDS,
  [ProductType.Meituan]: MEITUAN_GOODS_FIELDS,
  [ProductType.Eleme]: ELEME_GOODS_FIELDS,
  [ProductType.MeituanShangou]: MEITUAN_SHANGOU_GOODS_FIELDS
};
