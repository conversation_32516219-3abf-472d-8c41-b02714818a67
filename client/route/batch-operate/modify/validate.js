import { isNil } from 'lodash';
import isBefore from 'date-fns/is_before';
import { validator } from '@youzan/retail-form';
import { isRetailSingleStore, isUnifiedShop } from '@youzan/utils-shop';
import { compatScheduleShelfValidator } from '@youzan/goods-domain-pc-components';

import { TIMING_SALE } from 'cpn/field/batch-operate/sale-time-field/cpn';
import { BuyLimitType } from 'cpn/field/batch-operate/buy-limit-field/constant';
import { MaxLength } from 'cpn/field/batch-operate/selling-point-field';
import { SaleType, SendTime } from 'cpn/field/batch-operate/online-sale-type/constant';
import {
  SAME_DELIVERY,
  DELIVERY_TEMPLATE
} from 'cpn/field/batch-operate/delivery-template-field/cpn';
import { isFalsy } from 'common/utils';
import { IsMU } from 'common/constants';
import { SubShopStatus } from 'common/types';

import { SpuNoMaxLen } from 'common/constants/spu';
import {
  STORAGE_GOODS_NAME,
  STORAGE_GOODS_NO,
  OFFLINE_GOODS_STATUS,
  OFFLINE_GOODS_NAME,
  ONLINE_GOODS_STATUS,
  ONLINE_GOODS_NAME,
  ONLINE_SALE_TIME,
  ONLINE_BUY_LIMIT,
  ONLINE_DELIVERY_TEMPLATE,
  ONLINE_LOGISTICS_TIMELINESS_TEMPLATE,
  ONLINE_ORIGIN,
  ONLINE_GOODS_NO,
  ONLINE_DISTRIBUTION,
  ONLINE_BARCODE,
  ONLINE_START_SALE_NUM,
  ONLINE_SALE_TYPE,
  SCHEDULE_DISPLAY_OFF,
  ONLINE_GOODS_ORG,
  ONLINE_FENXIAO_FIELDS,
  ONLINE_All_GOODS_FIELDS,
  ONLINE_HEAVY_CONTINUED,
  ONLINE_SCHEDULE_DISPLAY,
  OFFLINE_SCHEDULE_DISPLAY,
  OFFLINE_START_SALE_NUM,
  STORAGE_SKU_PATTERN
} from './fields';
import { isFenXiao, isSupportBindOrg } from './utils';

const validateGoodsStatus = (value = {}) => {
  const { display, subKdtIds = [] } = value;
  if (IsMU) {
    if (!isNil(display) && subKdtIds.length === 0) {
      return '请选择店铺';
    }
    if (subKdtIds.length > 0 && isNil(display)) {
      return '请选择上下架操作';
    }
    // 总店没有默认值，可以为空
  } else if (isNil(display)) {
    return '请选择上下架操作';
  }
};

const validateScheduleDisplay = (value = {}) => {
  const { subKdtIds = [] } = value;
  const validateRes = compatScheduleShelfValidator(value);

  if (validateRes) {
    return validateRes;
  }
  if (IsMU && subKdtIds.length === 0) {
    return '请选择店铺';
  }
};

/* ---------------------------------- start --------------------------------- */

export const STORAGE_GOODS_STATUS_VALIDATE = [
  {
    type: 'validator',
    rule: (value = {}) => {
      const { isSale, subKdtIds = [], display, affectChannelIds, isMaterial } = value;
      if (!isNil(isSale) && subKdtIds.length === 0) {
        return '请选择店铺';
      }
      if (subKdtIds.length > 0 && isNil(isSale)) {
        return '请选择是否可售操作';
      }
      /** 选择可售时需要选择渠道级上下架 */
      if (
        !isMaterial &&
        isSale === SubShopStatus.Sale &&
        (isNil(display) || affectChannelIds.length === 0)
      ) {
        return '选择可售时，需要确认渠道级上下架状态';
      }
    }
  }
];

export const STORAGE_MATERIAL_GOODS_STATUS_VALIDATE = [
  {
    type: 'validator',
    rule: (value = {}) => {
      const { isSale, subKdtIds = [], display, affectChannelIds } = value;
      if (!isNil(isSale) && subKdtIds.length === 0) {
        return '请选择店铺';
      }
      if (subKdtIds.length > 0 && isNil(isSale)) {
        return '请选择是否可售操作';
      }
    }
  }
];

export const GOODS_NAME_VALIDATE = [
  {
    type: 'required',
    message: '请填写名称'
  }
];

export const INNER_ORG_VALIDATE = [
  {
    type: 'required',
    message: '请选择店内组织'
  }
];

export const OFFLINE_GOODS_STATUS_VALIDATE = [
  {
    type: 'validator',
    rule: validateGoodsStatus
  }
];

export const ONLINE_GOODS_STATUS_VALIDATE = [
  {
    type: 'validator',
    rule: validateGoodsStatus
  }
];

export const ONLINE_SALE_TIME_VALIDATE = [
  {
    type: 'validator',
    rule: (value = {}) => {
      const { isTimingSold, saleTime } = value;
      if (isNil(isTimingSold)) {
        return '请选择开售时间';
      }
      if (isTimingSold === TIMING_SALE && isNil(saleTime)) {
        return '请选择定时开售时间';
      }
    }
  }
];

export const SCHEDULE_DISPLAY_OFF_VALIDATE = [
  {
    type: 'validator',
    rule: (value = {}) => {
      const { enableScheduleDisplayOff, scheduleDisplayOffTime, subKdtIds = [] } = value;
      if (enableScheduleDisplayOff) {
        if (!scheduleDisplayOffTime) {
          return '请选择一个定时下架时间';
        }
        if (isBefore(new Date(scheduleDisplayOffTime), new Date())) {
          return '定时下架时间需大于当前时间';
        }
      }
      if (IsMU && enableScheduleDisplayOff !== undefined) {
        if (subKdtIds.length === 0) {
          return '请选择店铺';
        }
      }
    }
  }
];

export const BUY_LIMIT_VALIDATE = [
  {
    type: 'validator',
    rule: (value = {}) => {
      const { buyLimitType, buyLimitNum } = value || {};
      if (buyLimitType === BuyLimitType.NoQuota) {
        return false;
      }
      if (!buyLimitNum) {
        return '请输入件数';
      }
      if (+buyLimitNum > 9999999) {
        return '超过限购数量限制';
      }
    }
  }
];

export const ONLINE_DELIVERY_TEMPLATE_VALIDATE = [
  {
    type: 'validator',
    rule: (value = {}) => {
      const { type, postage, deliveryTemplateId, subKdtIds = [] } = value;

      if (type === SAME_DELIVERY) {
        if (isFalsy(postage)) {
          return '请填写统一运费';
        }
      } else if (type === DELIVERY_TEMPLATE) {
        if (!deliveryTemplateId) {
          return '请选择运费模板';
        }
      }

      if (isNil(type) && subKdtIds.length > 0) {
        return '请选择运费模板';
      }
    }
  }
];

export const ONLINE_LOGISTICS_TIMELINESS_TEMPLATE_VALIDATE = [
  {
    type: 'validator',
    rule: (value = {}) => {
      const { isTimelinessTemplateEnable, timelinessTemplateId } = value;

      if (isTimelinessTemplateEnable) {
        if (!timelinessTemplateId) {
          return '请选择物流时效模板';
        }
      }
    }
  }
];

export const ONLINE_ORIGIN_VALIDATE = [
  {
    type: 'validator',
    rule: (value = {}) => {
      if (String(value).length > 30) {
        return '划线价长度最大长度为30';
      }
    }
  }
];

export const ONLINE_GOODS_NO_VALIDATE = [
  {
    type: 'validator',
    rule: (value = '') => {
      if (String(value).length > SpuNoMaxLen) {
        return `最大输入长度为${SpuNoMaxLen}`;
      }
    }
  }
];

export const STORAGE_GOODS_NO_VALIDATE = [
  {
    type: 'validator',
    rule: value => {
      if (isNil(value)) {
        return '请输入商品条码';
      }

      if (String(value).length > 32) {
        return '最大输入长度为32';
      }
    }
  }
];

export const STORAGE_GOODS_CODE_VALIDATE = [
  {
    type: 'validator',
    rule: value => {
      if (isNil(value)) {
        return '请输入商品编码';
      }

      if (String(value).length > SpuNoMaxLen) {
        return `最大输入长度为${SpuNoMaxLen}`;
      }
    }
  }
];

export const STORAGE_SKU_PATTERN_VALIDATE = [
  {
    type: 'validator',
    rule: (value = '') => {
      if (value && String(value).length > 100) {
        return '规格型号最多100个字';
      }
    }
  }
];

export const STORAGE_INPUT_TAX_RATE_VALIDATE = [
  {
    type: 'required',
    message: '请选择进项税率'
  }
];

export const STORAGE_SALES_TAX_RATE_VALIDATE = [
  {
    type: 'required',
    message: '请选择销项税率'
  }
];

export const ONLINE_DISTRIBUTION_VALIDATE = [
  {
    type: 'validator',
    rule: (value = {}) => {
      if (Object.values(value).every(item => !item)) {
        return '请选择配送方式';
      }
    }
  }
];

export const ONLINE_HEAVY_CONTINUED_VALIDATE = [
  {
    type: 'validator',
    rule: value => {
      if (isNil(value)) {
        return '请选择续重收费';
      }
    }
  }
];

export const ONLINE_BARCODE_VALIDATE = [
  {
    type: 'validator',
    rule: (value = '') => {
      if (!/^[a-zA-Z0-9]{0,32}$/.test(value)) {
        return '仅支持填写数字和字母，最大长度32个字';
      }
    }
  }
];

export const ONLINE_SALE_NUM_VALIDATE = [
  {
    type: 'validator',
    rule: (value = '') => {
      if (isRetailSingleStore || isUnifiedShop) {
        if (value && !/^[1-9]\d{0,4}$/.test(value)) {
          return '请输入1-99999的数值';
        }
        return false;
      }
      if (value && !/^[1-9]\d{0,2}$/.test(value)) {
        return '请输入1-999的数值';
      }
    }
  }
];

export const OFFLINE_SALE_NUM_VALIDATE = [
  {
    type: 'validator',
    rule: value => {
      if (!value) {
        return;
      }
      const { offlineStartSaleNum, offlineIncrementSaleNum } = value;
      if (!+offlineStartSaleNum && +offlineIncrementSaleNum) {
        return '起售量不能为空';
      }
      if (+offlineStartSaleNum && !+offlineIncrementSaleNum) {
        return '增量不能为空';
      }
      if (+offlineIncrementSaleNum > +offlineStartSaleNum) {
        return '增量下单数不能大于起售量';
      }
      return void 0;
    }
  }
];

export const ONLINE_SALE_TYPE_VALIDATE = [
  {
    type: 'validator',
    rule: (value = {}) => {
      const { preSaleType, etdType, etdStart, etdDays } = value;
      if (preSaleType === SaleType.CashSale) {
        return false;
      }

      if (etdType === SendTime.StartTime && !etdStart) {
        return '请选择预计发货时间';
      }
      if (etdType === SendTime.PaiedDays && !etdDays) {
        return '请输入预付款后几天发货';
      }
      if (etdType === SendTime.PaiedDays && +etdDays > 90) {
        return '只允许设置90天内的发货时间';
      }
    }
  }
];

export const OFFLINE_SCHEDULE_DISPLAY_VALIDATE = [
  {
    type: 'validator',
    rule: validateScheduleDisplay
  }
];

export const ONLINE_SCHEDULE_DISPLAY_VALIDATE = [
  {
    type: 'validator',
    rule: validateScheduleDisplay
  }
];

/** 商品卖点校验 */
export const SELLING_POINT_VALIDATE = [
  {
    type: 'validator',
    rule: value => {
      if (String(value).length > MaxLength) {
        return `最大输入长度为${MaxLength}`;
      }
    }
  }
];

/* ----------------------------------- end ---------------------------------- */

export const FIELD_VALIDATE_MAP = {
  [STORAGE_GOODS_NAME]: GOODS_NAME_VALIDATE,
  [STORAGE_GOODS_NO]: STORAGE_GOODS_NO_VALIDATE,
  [STORAGE_SKU_PATTERN]: STORAGE_SKU_PATTERN_VALIDATE,
  [OFFLINE_GOODS_STATUS]: OFFLINE_GOODS_STATUS_VALIDATE,
  [OFFLINE_GOODS_NAME]: GOODS_NAME_VALIDATE,
  [ONLINE_GOODS_STATUS]: ONLINE_GOODS_STATUS_VALIDATE,
  [ONLINE_GOODS_NAME]: GOODS_NAME_VALIDATE,
  [ONLINE_SALE_TIME]: ONLINE_SALE_TIME_VALIDATE,
  [ONLINE_BUY_LIMIT]: BUY_LIMIT_VALIDATE,
  [ONLINE_DELIVERY_TEMPLATE]: ONLINE_DELIVERY_TEMPLATE_VALIDATE,
  [ONLINE_LOGISTICS_TIMELINESS_TEMPLATE]: ONLINE_LOGISTICS_TIMELINESS_TEMPLATE_VALIDATE,
  [ONLINE_ORIGIN]: ONLINE_ORIGIN_VALIDATE,
  [ONLINE_GOODS_NO]: ONLINE_GOODS_NO_VALIDATE,
  [ONLINE_DISTRIBUTION]: ONLINE_DISTRIBUTION_VALIDATE,
  [STORAGE_GOODS_NO]: STORAGE_GOODS_NO_VALIDATE,
  [ONLINE_BARCODE]: ONLINE_BARCODE_VALIDATE,
  [ONLINE_START_SALE_NUM]: ONLINE_SALE_NUM_VALIDATE,
  [OFFLINE_START_SALE_NUM]: OFFLINE_SALE_NUM_VALIDATE,
  [ONLINE_SALE_TYPE]: ONLINE_SALE_TYPE_VALIDATE,
  [SCHEDULE_DISPLAY_OFF]: SCHEDULE_DISPLAY_OFF_VALIDATE,
  [ONLINE_GOODS_ORG]: INNER_ORG_VALIDATE,
  [ONLINE_HEAVY_CONTINUED]: ONLINE_HEAVY_CONTINUED_VALIDATE,
  [ONLINE_SCHEDULE_DISPLAY]: ONLINE_SCHEDULE_DISPLAY_VALIDATE,
  [OFFLINE_SCHEDULE_DISPLAY]: OFFLINE_SCHEDULE_DISPLAY_VALIDATE
};

export const checkItems = (items, fields) => {
  const validItems = [];
  const invalidItems = [];

  if (Array.isArray(items)) {
    items.forEach(item => {
      const validateList = (fields || []).map(field => {
        const validateRule = FIELD_VALIDATE_MAP[field];
        if (field === 'onlineShopOrg' && !isSupportBindOrg(item)) {
          return false;
        }
        // 网店-分销商品跳过部分校验
        if (
          isFenXiao(item.goodsType) &&
          ONLINE_All_GOODS_FIELDS.find(item => item.value === field) &&
          !ONLINE_FENXIAO_FIELDS.find(item => item.value === field)
        ) {
          return false;
        }
        if (validateRule) {
          return validator(item[field], validateRule);
        }
        return false;
      });

      validateList.some(res => !!res) ? invalidItems.push(item) : validItems.push(item);
    });
  }

  return {
    validItems,
    invalidItems
  };
};
