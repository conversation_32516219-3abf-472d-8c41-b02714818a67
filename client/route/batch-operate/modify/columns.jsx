import React from 'react';
import { get, compact } from 'lodash';
import { Field } from '@youzan/retail-form';
import { SupplierSelectField } from '@youzan/biz-select-center';
import { isUnifiedHqStore, isWscSingleStore } from '@youzan/utils-shop';
import { GoodsBrandSelector } from '@youzan/retail-components';
import '@youzan/retail-components/lib/styles/batch-action-title.css';
import { InventoryType, ValuationType } from '@youzan/zan-hasaki';
import { BuyLimitType } from 'cpn/field/batch-operate/buy-limit-field/constant';

import { GoodsChannel, HasOfflineAbility, IsUnityModelWithoutFwh } from 'common/constants';
import {
  STORAGE_GOODS_STATUS,
  STORAGE_GOODS_CATEGORY,
  STORAGE_GOODS_NAME,
  GOODS_SUPPLIER,
  GOODS_BRAND,
  GOODS_LIFE_CYCLE,
  DELIVERY_TYPE,
  STORAGE_CATEGORY,
  STORAGE_GOODS_NO,
  OFFLINE_CHANNEL,
  ONLINE_CHANNEL,
  OFFLINE_GOODS_STATUS,
  OFFLINE_GOODS_NAME,
  OFFLINE_MEMBER_DISCOUNT,
  OFFLINE_GOODS_GROUP,
  ONLINE_GOODS_STATUS,
  ONLINE_GOODS_GROUP,
  ONLINE_GOODS_TEMPLATE,
  ONLINE_GOODS_NAME,
  ONLINE_MEMBER_DISCOUNT,
  ONLINE_SALE_TIME,
  SCHEDULE_DISPLAY_OFF,
  ONLINE_BUY_LIMIT,
  ONLINE_DELIVERY_TEMPLATE,
  ONLINE_LOGISTICS_TIMELINESS_TEMPLATE,
  SPU_SELL_CHANNEL,
  SPU_PRODUCTION_TIME,
  ONLINE_CATEGORY,
  ONLINE_ORIGIN,
  ONLINE_AFTER_SALE,
  ONLINE_GOODS_NO,
  ONLINE_DISTRIBUTION,
  ONLINE_BARCODE,
  ONLINE_START_SALE_NUM,
  ONLINE_SHOW_STOCK,
  ONLINE_PREPARE_TIME,
  ONLINE_SALE_TYPE,
  ONLINE_PRODUCTION_TIME,
  STORAGE_GOODS_CODE,
  ONLINE_CLASSIFICATION,
  ONLINE_BRAND,
  ONLINE_GOODS_ORG,
  GOODS_ATTR,
  ONLINE_HEAVY_CONTINUED,
  OFFLINE_SCHEDULE_DISPLAY,
  ONLINE_SCHEDULE_DISPLAY,
  SELL_POINT,
  PURCHASE_RIGHT_REQUEST,
  OFFLINE_BUY_LIMIT,
  OFFLINE_START_SALE_NUM,
  STORAGE_SKU_PATTERN,
  STORAGE_INPUT_TAX_RATE,
  STORAGE_SALES_TAX_RATE,
  STORAGE_SHELF_LIFE_WARNING,
  MEITUAN_GOODS_GROUP,
  ELEME_GOODS_GROUP,
  MEITUAN_SHANGOU_GOODS_GROUP,
  ONLINE_GOODS_INDEX
} from 'route/batch-operate/modify/fields';
import BatchActionHeader from 'cpn/with-batch-change-pop';

import {
  StorageGoodsStatusField,
  GoodsCategoryField,
  LifeCycleSelectField,
  DeliveryTypeField,
  OfflineGoodsStatusField,
  DiscountSelectField,
  OfflineGroupSelectField,
  OnlineGoodsStatusField,
  GoodsTemplateSelectField,
  SaleTimeField,
  BuyLimitField,
  IdLimitField,
  BatchRenameField,
  DeliveryTemplateField,
  LogisticsTimelinessTemplateField,
  OriginPriceField,
  CategoryParam,
  GoodsNoField,
  DistributionField,
  AfterSaleField,
  OnlineGoodsGroup,
  BarcodeField,
  SaleNumStartField,
  SaleNumStartAddField,
  ShowStockField,
  PrepareTimeField,
  SaleTypeField,
  ProductionTimeField,
  ScheduleDisplayOffField,
  GoodsClassificationSelectField,
  GoodsBrandSelectField,
  ShopCounterCascade,
  ShopCounterDialog,
  AttrSettingDialog,
  HeavyContinuedField,
  ScheduleDisplayField,
  SellingPointField,
  // OfflineBuyLimitField,
  SkuPatternField,
  InputTaxRateField,
  SalesTaxRateField,
  ShelfLifeWarningField,
  ExternalGroupField,
  ShangouExternalGroupField,
  GoodsIndexField
} from 'cpn/field/batch-operate';
import { validateProductionTime } from 'cpn/field/batch-operate/production-time-field';
import { MaxLength } from 'cpn/field/batch-operate/selling-point-field';

import { SalesChannelEnum } from '@youzan/goods-domain-definitions';
import { global } from '@youzan/retail-utils';
import ChannelSelect from './channel-select';
import BatchChannelSetting from './batch-channel-setting';
import AsyncDefaultShopListField from '../components/shop/async-default-shop-list-field';
import { getTotalOfflineChannel, getTotalOnlineChannel } from '../utils';
import {
  STORAGE_GOODS_STATUS_VALIDATE,
  OFFLINE_GOODS_STATUS_VALIDATE,
  ONLINE_GOODS_STATUS_VALIDATE,
  ONLINE_SALE_TIME_VALIDATE,
  SCHEDULE_DISPLAY_OFF_VALIDATE,
  BUY_LIMIT_VALIDATE,
  ONLINE_DELIVERY_TEMPLATE_VALIDATE,
  GOODS_NAME_VALIDATE,
  INNER_ORG_VALIDATE,
  ONLINE_ORIGIN_VALIDATE,
  ONLINE_GOODS_NO_VALIDATE,
  STORAGE_GOODS_NO_VALIDATE,
  ONLINE_BARCODE_VALIDATE,
  ONLINE_SALE_NUM_VALIDATE,
  ONLINE_SALE_TYPE_VALIDATE,
  STORAGE_GOODS_CODE_VALIDATE,
  OFFLINE_SCHEDULE_DISPLAY_VALIDATE,
  ONLINE_SCHEDULE_DISPLAY_VALIDATE,
  SELLING_POINT_VALIDATE,
  OFFLINE_SALE_NUM_VALIDATE,
  STORAGE_SKU_PATTERN_VALIDATE,
  STORAGE_MATERIAL_GOODS_STATUS_VALIDATE,
  STORAGE_INPUT_TAX_RATE_VALIDATE,
  STORAGE_SALES_TAX_RATE_VALIDATE,
  STORAGE_SHELF_LIFE_WARNING_VALIDATE
} from './validate';
import { batchCodeText } from '../constant';
import {
  checkIfCanNotEditSPUProductionTime,
  checkIfCanNotEditOnlineProductionTime,
  isSupportBindOrg,
  isFenXiao,
  hasWeight,
  isHaitao,
  getPreSaleExtraData
} from './utils';
import { cacheRequest } from './cache';
import { pop } from './index.scss';

const { BUSINESS } = global;
const { Field: GoodsBrandSelectorField } = GoodsBrandSelector;
const { CategoryBatchCpn, CategoryColumnCpn } = CategoryParam;
const { DistributionColumnCpn, DistributionBatchCpn } = DistributionField;
const { AfterSaleColumnCpn, AfterSaleBatchCpn } = AfterSaleField;
const { GoodsGroupSelectField, BatchOperateGroupField } = OnlineGoodsGroup;
const { StockShowBatch, StockShowColumn } = ShowStockField;
const { PrepareTimeBatch, PrepareTimeColumn } = PrepareTimeField;
const { SaleNumStartBatchCpn, SaleNumStartColumnCpn } = SaleNumStartField;
const { GoodsIndexBatchCpn, GoodsIndexColumnCpn } = GoodsIndexField;
const { SaleNumStartAddBatchCpn, SaleNumStartAddColumnCpn } = SaleNumStartAddField;
const { SaleTypeBatchCpn, SaleTypeColumnCpn } = SaleTypeField;
const { BuyLimitBatchCpn, BuyLimitColumnCpn } = BuyLimitField;
const { IdLimitBatchCpn, IdLimitColumnCpn } = IdLimitField;
const { RenameBatchCpn } = BatchRenameField;
const { ScheduleDisplayOffBatchCpn, ScheduleDisplayOffColumnCpn } = ScheduleDisplayOffField;
const {
  GoodsClassificationBatchCpn,
  GoodsClassificationColumnCpn
} = GoodsClassificationSelectField;
const { GoodsBrandBatchCpn, GoodsBrandColumnCpn } = GoodsBrandSelectField;
const {
  LogisticsTimelinessTemplateBatchCpn,
  LogisticsTimelinessTemplateColumnCpn
} = LogisticsTimelinessTemplateField;
const { AttrSettingBatchCpn, AttrSettingColCpn } = AttrSettingDialog;
const { InputTaxRateBatch, InputTaxRateColumn } = InputTaxRateField;
const { SalesTaxRateBatch, SalesTaxRateColumn } = SalesTaxRateField;
const { ShelfLifeWarningBatch, ShelfLifeWarningColumn } = ShelfLifeWarningField;
const getColumns = props => {
  const {
    rule,
    isErrorItems,
    onBatchSet,
    onBatchChangeName,
    onBatchChangeGroups,
    onBatchChangeChannel,
    onBatchCategorySet,
    onBatchDialogSet,
    onShopCounterChange,
    fields,
    initialize,
    untouch,
    isCategoryCredentials,
    isCategoryFetching,
    categoryFailInfo = {},
    retry,
    origin,
    items
  } = props;
  const hasHaitao = items.some(item => isHaitao(item.bizMarkCode));
  const isMaterial = items.every(item => item.inventoryType === InventoryType.Material);

  /* ------------------------------ 商品库商品 columns ----------------------------- */

  const STORAGE_GOODS_COLUMN = {
    [STORAGE_GOODS_STATUS]: {
      title: (
        <BatchActionHeader
          title="是否可售"
          titleOnly={isErrorItems}
          component={StorageGoodsStatusField}
          onConfirm={onBatchSet}
          props={{
            name: `batch_${STORAGE_GOODS_STATUS}`,
            targetName: STORAGE_GOODS_STATUS,
            isMaterial
          }}
        />
      ),
      name: STORAGE_GOODS_STATUS,
      bodyRender: ({ field, inventoryType }) => {
        const isMaterial = inventoryType === InventoryType.Material;
        return (
          <StorageGoodsStatusField
            name={`${field}.${STORAGE_GOODS_STATUS}`}
            validate={
              isMaterial ? STORAGE_MATERIAL_GOODS_STATUS_VALIDATE : STORAGE_GOODS_STATUS_VALIDATE
            }
            isMaterial={isMaterial}
          />
        );
      }
    },
    [STORAGE_GOODS_CATEGORY]: {
      title: (
        <BatchActionHeader
          title="商品分类"
          titleOnly={isErrorItems}
          component={GoodsCategoryField}
          onConfirm={onBatchSet}
          props={{
            name: `batch_${STORAGE_GOODS_CATEGORY}`,
            targetName: STORAGE_GOODS_CATEGORY
          }}
        />
      ),
      name: STORAGE_GOODS_CATEGORY,
      bodyRender: ({ field }) => {
        return <GoodsCategoryField name={`${field}.${STORAGE_GOODS_CATEGORY}`} />;
      }
    },
    [ONLINE_GOODS_ORG]: {
      title: (
        <BatchActionHeader
          title="店内组织"
          withDialog
          titleOnly={isErrorItems}
          popTitle="批量修改店内组织"
          component={ShopCounterDialog}
          onConfirm={onShopCounterChange}
          dialogProps={{ style: { minWidth: '358px', width: '358px' } }}
          props={{
            name: `batch_${ONLINE_GOODS_ORG}`,
            targetName: ONLINE_GOODS_ORG
          }}
        />
      ),
      name: ONLINE_GOODS_ORG,
      bodyRender: data => {
        const { field } = data;
        // 当可选 店内组织时 说明有店铺能力+有权限
        // 只支持实物商品和海淘商品修改
        if (!isSupportBindOrg(data)) return null;
        return (
          <Field
            name={`${field}.${ONLINE_GOODS_ORG}`}
            validate={INNER_ORG_VALIDATE}
            component={ShopCounterCascade}
            props={{ isErrorTable: !!isErrorItems }}
          />
        );
      }
    },
    [STORAGE_GOODS_NAME]: {
      title: (
        <BatchActionHeader
          title="商品名称"
          titleOnly={isErrorItems}
          component={RenameBatchCpn}
          onConfirm={onBatchChangeName}
          props={{
            name: `batch_${STORAGE_GOODS_NAME}`,
            targetName: STORAGE_GOODS_NAME,
            rule,
            initialize,
            untouch
          }}
        />
      ),
      name: STORAGE_GOODS_NAME,
      bodyRender: ({ field }) => {
        return (
          <Field
            name={`${field}.${STORAGE_GOODS_NAME}`}
            validate={GOODS_NAME_VALIDATE}
            component="InputField"
          />
        );
      }
    },
    [GOODS_SUPPLIER]: {
      title: (
        <BatchActionHeader
          title="首选供应商"
          titleOnly={isErrorItems}
          component={SupplierSelectField}
          onConfirm={onBatchSet}
          props={{
            label: null,
            name: `batch_${GOODS_SUPPLIER}`,
            targetName: GOODS_SUPPLIER,
            props: {
              showTotal: false
            }
          }}
        />
      ),
      name: GOODS_SUPPLIER,
      bodyRender: ({ field }) => {
        return (
          <SupplierSelectField
            label={null}
            props={{ showTotal: false, isNeedCycleFind: true }}
            name={`${field}.${GOODS_SUPPLIER}`}
          />
        );
      }
    },
    [GOODS_BRAND]: {
      title: (
        <BatchActionHeader
          title="商品品牌"
          titleOnly={isErrorItems}
          component={GoodsBrandSelectorField}
          onConfirm={onBatchSet}
          props={{
            name: `batch_${GOODS_BRAND}`,
            targetName: GOODS_BRAND,
            type: 'RF'
          }}
        />
      ),
      name: GOODS_BRAND,
      bodyRender: ({ field }) => {
        return <GoodsBrandSelectorField name={`${field}.${GOODS_BRAND}`} type="RF" />;
      }
    },
    [GOODS_LIFE_CYCLE]: {
      title: (
        <BatchActionHeader
          title="生命周期"
          titleOnly={isErrorItems}
          component={LifeCycleSelectField}
          onConfirm={onBatchSet}
          props={{
            name: `batch_${GOODS_LIFE_CYCLE}`,
            targetName: GOODS_LIFE_CYCLE
          }}
        />
      ),
      name: GOODS_LIFE_CYCLE,
      bodyRender: ({ field }) => {
        return <LifeCycleSelectField name={`${field}.${GOODS_LIFE_CYCLE}`} />;
      }
    },
    [DELIVERY_TYPE]: {
      title: (
        <BatchActionHeader
          title="物流模式"
          titleOnly={isErrorItems}
          component={DeliveryTypeField}
          onConfirm={onBatchSet}
          props={{
            name: `batch_${DELIVERY_TYPE}`,
            targetName: DELIVERY_TYPE
          }}
        />
      ),
      name: DELIVERY_TYPE,
      bodyRender: ({ field }) => {
        return <DeliveryTypeField name={`${field}.${DELIVERY_TYPE}`} />;
      }
    },
    [STORAGE_CATEGORY]: {
      title: (
        <BatchActionHeader
          withPop
          title="商品类目"
          popTitle="商品类目"
          titleOnly={isErrorItems}
          component={CategoryBatchCpn}
          onConfirm={onBatchCategorySet}
          props={{
            name: `batch_${STORAGE_CATEGORY}`,
            targetName: STORAGE_CATEGORY,
            isCategoryFetching,
            categoryFailInfo,
            retry
          }}
        />
      ),
      width: 350,
      name: STORAGE_CATEGORY,
      bodyRender: ({ field }) => {
        return (
          <CategoryColumnCpn
            isCategoryCredentials={isCategoryCredentials}
            name={`${field}.${STORAGE_CATEGORY}`}
          />
        );
      }
    },
    [STORAGE_GOODS_NO]: {
      title: (
        <BatchActionHeader
          title="商品条码"
          titleOnly={isErrorItems}
          component={GoodsNoField}
          onConfirm={onBatchSet}
          props={{
            name: `batch_${STORAGE_GOODS_NO}`,
            targetName: STORAGE_GOODS_NO,
            label: '商品条码：'
          }}
        />
      ),
      width: 300,
      name: STORAGE_GOODS_NO,
      bodyRender: ({ field }) => {
        return (
          <Field
            name={`${field}.${STORAGE_GOODS_NO}`}
            component="InputField"
            placeholder="请输入字母或数字"
            validate={STORAGE_GOODS_NO_VALIDATE}
          />
        );
      }
    },
    [SPU_PRODUCTION_TIME]: {
      title: (
        <BatchActionHeader
          title="加工时长"
          titleOnly={isErrorItems}
          component={ProductionTimeField}
          onConfirm={onBatchSet}
          props={{
            name: `batch_${SPU_PRODUCTION_TIME}`,
            targetName: SPU_PRODUCTION_TIME,
            shouldIgnoreChangingItem: item => {
              return checkIfCanNotEditSPUProductionTime(item);
            },
            validateBatchChangingValue: value => {
              return validateProductionTime(value);
            },
            label: null
          }}
        />
      ),
      width: 400,
      name: SPU_PRODUCTION_TIME,
      bodyRender: item => {
        return (
          <ProductionTimeField
            name={`${item.field}.${SPU_PRODUCTION_TIME}`}
            label={null}
            disabled={checkIfCanNotEditSPUProductionTime(item)}
          />
        );
      }
    },
    [STORAGE_GOODS_CODE]: {
      title: (
        <BatchActionHeader
          title="商品编码"
          titleOnly={isErrorItems}
          component={GoodsNoField}
          onConfirm={onBatchSet}
          props={{
            name: `batch_${STORAGE_GOODS_CODE}`,
            targetName: STORAGE_GOODS_CODE,
            label: '商品编码：'
          }}
        />
      ),
      width: 300,
      name: STORAGE_GOODS_CODE,
      bodyRender: ({ field }) => {
        return (
          <Field
            name={`${field}.${STORAGE_GOODS_CODE}`}
            component="InputField"
            placeholder="请输入"
            validate={STORAGE_GOODS_CODE_VALIDATE}
          />
        );
      }
    },
    [GOODS_ATTR]: {
      title: (
        <BatchActionHeader
          title="商品属性"
          withDialog
          titleOnly={isErrorItems}
          component={AttrSettingBatchCpn}
          dialogProps={{ style: { width: 'auto' } }}
          onConfirm={onBatchDialogSet}
          props={{
            name: `batch_${GOODS_ATTR}`,
            targetName: GOODS_ATTR,
            shouldIgnoreChangingItem: ({ inventoryType }) => {
              return inventoryType === InventoryType.Material;
            },
            showTip: BUSINESS.skuAttrSwitch
          }}
        />
      ),
      width: 400,
      name: GOODS_ATTR,
      bodyRender: data => {
        const { field, inventoryType } = data;
        // 原材料不能设置属性
        if (inventoryType === InventoryType.Material) {
          return '-';
        }
        return (
          <AttrSettingColCpn
            showTip={BUSINESS.skuAttrSwitch}
            name={`${field}.${GOODS_ATTR}`}
            data={data}
          />
        );
      }
    },
    [STORAGE_SKU_PATTERN]: {
      title: (
        <BatchActionHeader
          title="规格型号"
          titleOnly={isErrorItems}
          component={SkuPatternField}
          onConfirm={onBatchSet}
          props={{
            name: `batch_${STORAGE_SKU_PATTERN}`,
            targetName: STORAGE_SKU_PATTERN,
            label: '规格型号：',
            shouldIgnoreChangingItem: ({ isNonSpec }) => {
              return isNonSpec === false;
            }
          }}
        />
      ),
      width: 300,
      name: STORAGE_SKU_PATTERN,
      bodyRender: data => {
        const { field, isNonSpec } = data;
        // 多规格商品不支持设置规格型号
        if (isNonSpec === false) {
          return '-';
        }
        return (
          <Field
            name={`${field}.${STORAGE_SKU_PATTERN}`}
            component="InputField"
            placeholder="请输入"
            validate={STORAGE_SKU_PATTERN_VALIDATE}
          />
        );
      }
    },
    [STORAGE_INPUT_TAX_RATE]: {
      title: (
        <BatchActionHeader
          title="进项税率"
          titleOnly={isErrorItems}
          component={InputTaxRateBatch}
          onConfirm={onBatchSet}
          props={{
            name: `batch_${STORAGE_INPUT_TAX_RATE}`,
            targetName: STORAGE_INPUT_TAX_RATE
          }}
        />
      ),
      width: 200,
      name: STORAGE_INPUT_TAX_RATE,
      bodyRender: ({ field }) => {
        return (
          <InputTaxRateColumn
            name={`${field}.${STORAGE_INPUT_TAX_RATE}`}
            validate={STORAGE_INPUT_TAX_RATE_VALIDATE}
          />
        );
      }
    },
    [STORAGE_SALES_TAX_RATE]: {
      title: (
        <BatchActionHeader
          title="销项税率"
          titleOnly={isErrorItems}
          component={SalesTaxRateBatch}
          onConfirm={onBatchSet}
          props={{
            name: `batch_${STORAGE_SALES_TAX_RATE}`,
            targetName: STORAGE_SALES_TAX_RATE
          }}
        />
      ),
      width: 200,
      name: STORAGE_SALES_TAX_RATE,
      bodyRender: ({ field }) => {
        return (
          <SalesTaxRateColumn
            name={`${field}.${STORAGE_SALES_TAX_RATE}`}
            validate={STORAGE_SALES_TAX_RATE_VALIDATE}
          />
        );
      }
    },
    [STORAGE_SHELF_LIFE_WARNING]: {
      title: (
        <BatchActionHeader
          title="保质期管理"
          titleOnly={isErrorItems}
          component={ShelfLifeWarningBatch}
          onConfirm={onBatchSet}
          props={{
            name: `batch_${STORAGE_SHELF_LIFE_WARNING}`,
            targetName: STORAGE_SHELF_LIFE_WARNING
          }}
        />
      ),
      width: 300,
      name: STORAGE_SHELF_LIFE_WARNING,
      bodyRender: ({ field }) => {
        return (
          <ShelfLifeWarningColumn
            name={`${field}.${STORAGE_SHELF_LIFE_WARNING}`}
            validate={STORAGE_SHELF_LIFE_WARNING_VALIDATE}
          />
        );
      }
    }
  };

  if (isUnifiedHqStore) {
    STORAGE_GOODS_COLUMN[OFFLINE_CHANNEL] = {
      title: (
        <BatchActionHeader
          title="门店渠道"
          titleOnly={isErrorItems}
          onConfirm={() =>
            onBatchChangeChannel({
              channel: GoodsChannel.Offline,
              name: `batch_${OFFLINE_CHANNEL}`,
              targetName: OFFLINE_CHANNEL
            })
          }
          component={() => (
            <AsyncDefaultShopListField
              hideLabel
              name={`batch_${OFFLINE_CHANNEL}`}
              component={BatchChannelSetting}
              channel={GoodsChannel.Offline}
              getDefaultShopList={getTotalOfflineChannel}
            />
          )}
          popProps={{ className: pop }}
        />
      ),
      name: OFFLINE_CHANNEL,
      bodyRender: ({ field }) => (
        <AsyncDefaultShopListField
          hideLabel
          className="channel-select-field"
          name={`${field}${OFFLINE_CHANNEL}`}
          component={ChannelSelect}
          channel={GoodsChannel.Offline}
          getDefaultShopList={getTotalOfflineChannel}
        />
      )
    };
    STORAGE_GOODS_COLUMN[ONLINE_CHANNEL] = {
      title: (
        <BatchActionHeader
          title="网店渠道"
          titleOnly={isErrorItems}
          onConfirm={() =>
            onBatchChangeChannel({
              channel: GoodsChannel.Online,
              name: `batch_${ONLINE_CHANNEL}`,
              targetName: ONLINE_CHANNEL
            })
          }
          component={() => (
            <AsyncDefaultShopListField
              hideLabel
              name={`batch_${ONLINE_CHANNEL}`}
              component={BatchChannelSetting}
              channel={GoodsChannel.Online}
              getDefaultShopList={getTotalOnlineChannel}
            />
          )}
          popProps={{ className: pop }}
        />
      ),
      name: ONLINE_CHANNEL,
      bodyRender: ({ field }) => (
        <AsyncDefaultShopListField
          hideLabel
          className="channel-select-field"
          name={`${field}${ONLINE_CHANNEL}`}
          component={ChannelSelect}
          channel={GoodsChannel.Online}
          getDefaultShopList={getTotalOnlineChannel}
        />
      )
    };
  }

  /* ------------------------------ 门店商品 columns ------------------------------ */

  const OFFLINE_GOODS_COLUMN = {
    [OFFLINE_GOODS_STATUS]: {
      title: (
        <BatchActionHeader
          title="商品状态"
          titleOnly={isErrorItems}
          component={OfflineGoodsStatusField}
          onConfirm={onBatchSet}
          props={{
            name: `batch_${OFFLINE_GOODS_STATUS}`,
            targetName: OFFLINE_GOODS_STATUS
          }}
        />
      ),
      name: OFFLINE_GOODS_STATUS,
      width: 310,
      bodyRender: ({ field }) => {
        return (
          <OfflineGoodsStatusField
            validate={OFFLINE_GOODS_STATUS_VALIDATE}
            name={`${field}.${OFFLINE_GOODS_STATUS}`}
            showTip={false}
          />
        );
      }
    },
    [OFFLINE_GOODS_NAME]: {
      title: (
        <BatchActionHeader
          title="商品名称"
          titleOnly={isErrorItems}
          component={RenameBatchCpn}
          onConfirm={onBatchChangeName}
          props={{
            name: `batch_${OFFLINE_GOODS_NAME}`,
            targetName: OFFLINE_GOODS_NAME,
            rule,
            initialize,
            untouch
          }}
        />
      ),
      name: OFFLINE_GOODS_NAME,
      bodyRender: ({ field }) => {
        return (
          <Field
            name={`${field}.${OFFLINE_GOODS_NAME}`}
            validate={GOODS_NAME_VALIDATE}
            component="InputField"
          />
        );
      }
    },
    [OFFLINE_MEMBER_DISCOUNT]: {
      title: (
        <BatchActionHeader
          title="会员折扣"
          titleOnly={isErrorItems}
          component={DiscountSelectField}
          onConfirm={onBatchSet}
          props={{
            name: `batch_${OFFLINE_MEMBER_DISCOUNT}`,
            targetName: OFFLINE_MEMBER_DISCOUNT
          }}
        />
      ),
      name: OFFLINE_MEMBER_DISCOUNT,
      bodyRender: ({ field }) => {
        return <DiscountSelectField name={`${field}.${OFFLINE_MEMBER_DISCOUNT}`} />;
      }
    },
    [OFFLINE_GOODS_GROUP]: {
      title: (
        <BatchActionHeader
          title="商品分组"
          titleOnly={isErrorItems}
          component={OfflineGroupSelectField}
          onConfirm={onBatchSet}
          props={{
            name: `batch_${OFFLINE_GOODS_GROUP}`,
            targetName: OFFLINE_GOODS_GROUP
          }}
        />
      ),
      name: OFFLINE_GOODS_GROUP,
      bodyRender: ({ field }) => {
        return <OfflineGroupSelectField name={`${field}.${OFFLINE_GOODS_GROUP}`} />;
      }
    },
    [OFFLINE_SCHEDULE_DISPLAY]: {
      title: (
        <BatchActionHeader
          title="定时上下架"
          titleOnly={isErrorItems}
          component={ScheduleDisplayField}
          onConfirm={onBatchSet}
          props={{
            name: `batch_${OFFLINE_SCHEDULE_DISPLAY}`,
            targetName: OFFLINE_SCHEDULE_DISPLAY
          }}
        />
      ),
      width: 450,
      name: OFFLINE_SCHEDULE_DISPLAY,
      bodyRender: ({ field }) => {
        return (
          <ScheduleDisplayField
            name={`${field}.${OFFLINE_SCHEDULE_DISPLAY}`}
            targetName={OFFLINE_SCHEDULE_DISPLAY}
            validate={OFFLINE_SCHEDULE_DISPLAY_VALIDATE}
          />
        );
      }
    },
    [OFFLINE_BUY_LIMIT]: {
      title: (
        <BatchActionHeader
          title="限购"
          titleOnly={isErrorItems}
          component={BuyLimitBatchCpn}
          onConfirm={onBatchDialogSet}
          withDialog
          props={{
            name: `batch_${OFFLINE_BUY_LIMIT}`,
            targetName: OFFLINE_BUY_LIMIT,
            shouldIgnoreChangingItem: item => {
              return isFenXiao(item.goodsType);
            }
          }}
        />
      ),
      width: 320,
      name: OFFLINE_BUY_LIMIT,
      bodyRender: ({ field, goodsType }) => {
        if (isFenXiao(goodsType)) {
          return '-';
        }
        return (
          <BuyLimitColumnCpn name={`${field}.${OFFLINE_BUY_LIMIT}`} validate={BUY_LIMIT_VALIDATE} />
        );
      }
    },
    [OFFLINE_START_SALE_NUM]: {
      title: (
        <BatchActionHeader
          title="起售数量"
          titleOnly={isErrorItems}
          component={SaleNumStartAddBatchCpn}
          onConfirm={onBatchSet}
          props={{
            name: `batch_${OFFLINE_START_SALE_NUM}`,
            targetName: OFFLINE_START_SALE_NUM,
            shouldIgnoreChangingItem: item => {
              return isFenXiao(item.goodsType);
            }
          }}
        />
      ),
      width: 500,
      name: OFFLINE_START_SALE_NUM,
      bodyRender: ({ field, goodsType }) => {
        if (isFenXiao(goodsType)) {
          return '-';
        }
        return (
          <SaleNumStartAddColumnCpn
            name={`${field}.${OFFLINE_START_SALE_NUM}`}
            validate={OFFLINE_SALE_NUM_VALIDATE}
          />
        );
      }
    }
  };

  /* ------------------------------ 网店商品 columns ------------------------------ */
  const ONLINE_GOODS_COLUMN = {
    [ONLINE_GOODS_STATUS]: {
      title: (
        <BatchActionHeader
          title="商品状态"
          titleOnly={isErrorItems}
          component={OnlineGoodsStatusField}
          onConfirm={onBatchSet}
          props={{
            name: `batch_${ONLINE_GOODS_STATUS}`,
            targetName: ONLINE_GOODS_STATUS,
            shouldIgnoreChangingItem: item => {
              return isFenXiao(item.goodsType);
            }
          }}
        />
      ),
      width: 310,
      name: ONLINE_GOODS_STATUS,
      bodyRender: ({ field, goodsType }) => {
        if (isFenXiao(goodsType)) {
          return '-';
        }
        return (
          <OnlineGoodsStatusField
            validate={ONLINE_GOODS_STATUS_VALIDATE}
            name={`${field}.${ONLINE_GOODS_STATUS}`}
            showTip={false}
          />
        );
      }
    },
    [ONLINE_GOODS_GROUP]: {
      title: (
        <BatchActionHeader
          title="商品分组"
          titleOnly={isErrorItems}
          component={BatchOperateGroupField}
          onConfirm={onBatchChangeGroups}
          props={{
            name: `batch_${ONLINE_GOODS_GROUP}`,
            targetName: ONLINE_GOODS_GROUP,
            origin
          }}
        />
      ),
      name: ONLINE_GOODS_GROUP,
      bodyRender: ({ field, createdType }) => {
        return (
          <GoodsGroupSelectField
            name={`${field}.${ONLINE_GOODS_GROUP}`}
            props={{ origin, createdType, customRequest: cacheRequest }}
          />
        );
      }
    },
    [ONLINE_GOODS_TEMPLATE]: {
      title: (
        <BatchActionHeader
          title="商品页模板"
          helpDesc="设置用户端商品页模板，仅支持设置商家自定义的商品页模板"
          titleOnly={isErrorItems}
          component={GoodsTemplateSelectField}
          onConfirm={onBatchSet}
          props={{
            name: `batch_${ONLINE_GOODS_TEMPLATE}`,
            targetName: ONLINE_GOODS_TEMPLATE
          }}
        />
      ),
      width: 300,
      name: ONLINE_GOODS_TEMPLATE,
      bodyRender: ({ field }) => {
        return <GoodsTemplateSelectField name={`${field}.${ONLINE_GOODS_TEMPLATE}`} />;
      }
    },
    [ONLINE_GOODS_NAME]: {
      title: (
        <BatchActionHeader
          title="商品名称"
          titleOnly={isErrorItems}
          component={RenameBatchCpn}
          onConfirm={onBatchChangeName}
          props={{
            name: `batch_${ONLINE_GOODS_NAME}`,
            targetName: ONLINE_GOODS_NAME,
            rule,
            initialize,
            untouch
          }}
        />
      ),
      name: ONLINE_GOODS_NAME,
      bodyRender: ({ field }) => {
        return (
          <Field
            name={`${field}.${ONLINE_GOODS_NAME}`}
            validate={GOODS_NAME_VALIDATE}
            component="InputField"
          />
        );
      }
    },
    [SELL_POINT]: {
      title: (
        <BatchActionHeader
          title="商品卖点"
          titleOnly={isErrorItems}
          component={SellingPointField}
          onConfirm={onBatchSet}
          props={{
            name: `batch_${SELL_POINT}`,
            targetName: SELL_POINT
          }}
        />
      ),
      name: SELL_POINT,
      bodyRender: ({ field }) => {
        return (
          <Field
            name={`${field}.${SELL_POINT}`}
            validate={SELLING_POINT_VALIDATE}
            component="InputField"
            props={{ type: 'textarea', maxlength: MaxLength, placeholder: '请输入商品卖点' }}
          />
        );
      }
    },
    [ONLINE_MEMBER_DISCOUNT]: {
      title: (
        <BatchActionHeader
          title="会员折扣"
          titleOnly={isErrorItems}
          component={DiscountSelectField}
          onConfirm={onBatchSet}
          props={{
            name: `batch_${ONLINE_MEMBER_DISCOUNT}`,
            targetName: ONLINE_MEMBER_DISCOUNT
          }}
        />
      ),
      name: ONLINE_MEMBER_DISCOUNT,
      bodyRender: ({ field }) => {
        return <DiscountSelectField name={`${field}.${ONLINE_MEMBER_DISCOUNT}`} />;
      }
    },
    [ONLINE_SALE_TIME]: {
      title: (
        <BatchActionHeader
          title="开售时间"
          titleOnly={isErrorItems}
          component={SaleTimeField}
          onConfirm={onBatchSet}
          props={{
            name: `batch_${ONLINE_SALE_TIME}`,
            targetName: ONLINE_SALE_TIME,
            shouldIgnoreChangingItem: item => {
              return isFenXiao(item.goodsType);
            }
          }}
        />
      ),
      width: 320,
      name: ONLINE_SALE_TIME,
      bodyRender: ({ field, goodsType }) => {
        if (isFenXiao(goodsType)) {
          return '-';
        }
        return (
          <SaleTimeField
            name={`${field}.${ONLINE_SALE_TIME}`}
            validate={ONLINE_SALE_TIME_VALIDATE}
          />
        );
      }
    },
    [SCHEDULE_DISPLAY_OFF]: {
      title: (
        <BatchActionHeader
          title="定时下架"
          titleOnly={isErrorItems}
          component={ScheduleDisplayOffBatchCpn}
          onConfirm={onBatchDialogSet}
          withDialog
          props={{
            name: `batch_${SCHEDULE_DISPLAY_OFF}`,
            targetName: SCHEDULE_DISPLAY_OFF
          }}
          dialogProps={{
            maskClosable: false,
            style: {
              minWidth: 480
            }
          }}
        />
      ),
      width: 320,
      name: SCHEDULE_DISPLAY_OFF,
      bodyRender: ({ field }) => {
        return (
          <ScheduleDisplayOffColumnCpn
            name={`${field}.${SCHEDULE_DISPLAY_OFF}`}
            validate={SCHEDULE_DISPLAY_OFF_VALIDATE}
          />
        );
      }
    },
    [ONLINE_SCHEDULE_DISPLAY]: {
      title: (
        <BatchActionHeader
          title="定时上下架"
          titleOnly={isErrorItems}
          component={ScheduleDisplayField}
          onConfirm={onBatchSet}
          props={{
            name: `batch_${ONLINE_SCHEDULE_DISPLAY}`,
            targetName: ONLINE_SCHEDULE_DISPLAY
          }}
        />
      ),
      width: 450,
      name: ONLINE_SCHEDULE_DISPLAY,
      bodyRender: ({ field }) => {
        return (
          <ScheduleDisplayField
            name={`${field}.${ONLINE_SCHEDULE_DISPLAY}`}
            targetName={ONLINE_SCHEDULE_DISPLAY}
            validate={ONLINE_SCHEDULE_DISPLAY_VALIDATE}
          />
        );
      }
    },
    [ONLINE_BUY_LIMIT]: {
      title: (
        <BatchActionHeader
          title="每人限购数"
          titleOnly={isErrorItems}
          component={BuyLimitBatchCpn}
          onConfirm={onBatchDialogSet}
          withDialog
          props={{
            name: `batch_${ONLINE_BUY_LIMIT}`,
            targetName: ONLINE_BUY_LIMIT,
            shouldIgnoreChangingItem: item => {
              return isFenXiao(item.goodsType);
            },
            buyLimitTypes: isWscSingleStore
              ? undefined
              : compact([
                  IsUnityModelWithoutFwh ? BuyLimitType.OrderQuota : null,
                  BuyLimitType.Always,
                  BuyLimitType.PeriodQuota
                ])
          }}
        />
      ),
      width: 320,
      name: ONLINE_BUY_LIMIT,
      bodyRender: ({ field, goodsType }) => {
        if (isFenXiao(goodsType)) {
          return '-';
        }
        return (
          <BuyLimitColumnCpn
            name={`${field}.${ONLINE_BUY_LIMIT}`}
            validate={BUY_LIMIT_VALIDATE}
            props={{
              buyLimitTypes: isWscSingleStore
                ? undefined
                : compact([
                    IsUnityModelWithoutFwh ? BuyLimitType.OrderQuota : null,
                    BuyLimitType.Always,
                    BuyLimitType.PeriodQuota
                  ])
            }}
          />
        );
      }
    },
    [PURCHASE_RIGHT_REQUEST]: {
      title: (
        <BatchActionHeader
          title="身份限购"
          titleOnly={isErrorItems}
          component={IdLimitBatchCpn}
          onConfirm={onBatchDialogSet}
          withDialog
          props={{
            name: `batch_${PURCHASE_RIGHT_REQUEST}`,
            targetName: PURCHASE_RIGHT_REQUEST,
            shouldIgnoreChangingItem: item => {
              return isFenXiao(item.goodsType);
            }
          }}
        />
      ),
      width: 400,
      name: PURCHASE_RIGHT_REQUEST,
      bodyRender: ({ field, goodsType, itemId }) => {
        if (isFenXiao(goodsType)) {
          return '-';
        }
        return <IdLimitColumnCpn itemId={itemId} name={`${field}.${PURCHASE_RIGHT_REQUEST}`} />;
      }
    },
    [ONLINE_DELIVERY_TEMPLATE]: {
      title: (
        <BatchActionHeader
          title="运费模板"
          helpDesc='模板仅对实物商品生效，无重量商品不支持"计重模板"'
          titleOnly={isErrorItems}
          component={DeliveryTemplateField}
          onConfirm={onBatchSet}
          props={{
            name: `batch_${ONLINE_DELIVERY_TEMPLATE}`,
            targetName: ONLINE_DELIVERY_TEMPLATE,
            allowWeight: true,
            shouldIgnoreChangingItem: (item, batchRealValue) => {
              if (isFenXiao(item.goodsType)) {
                return true;
              }
              // 选择计重模板时需要进行校验
              if (batchRealValue?.valuationType === ValuationType.Weight && !isWscSingleStore) {
                return !hasWeight(item);
              }
              return false;
            }
          }}
        />
      ),
      width: 340,
      name: ONLINE_DELIVERY_TEMPLATE,
      bodyRender: ({
        field,
        goodsType,
        itemMeasModel,
        isMultiUnit,
        isNonSpec,
        stocks,
        itemSkuMarkAggregateModelList = []
      }) => {
        if (isFenXiao(goodsType)) {
          return '-';
        }
        const allowWeight = hasWeight({
          itemMeasModel,
          isMultiUnit,
          isNonSpec,
          stocks,
          itemSkuMarkAggregateModelList
        });

        return (
          <DeliveryTemplateField
            name={`${field}.${ONLINE_DELIVERY_TEMPLATE}`}
            validate={ONLINE_DELIVERY_TEMPLATE_VALIDATE}
            allowWeight={allowWeight}
          />
        );
      }
    },
    [ONLINE_LOGISTICS_TIMELINESS_TEMPLATE]: {
      title: (
        <BatchActionHeader
          title="物流时效模板"
          helpDesc="模板仅对实物商品生效，且不支持海淘商品、预售商品、周期购商品"
          titleOnly={isErrorItems}
          component={LogisticsTimelinessTemplateBatchCpn}
          onConfirm={onBatchDialogSet}
          withDialog
          props={{
            name: `batch_${ONLINE_LOGISTICS_TIMELINESS_TEMPLATE}`,
            targetName: ONLINE_LOGISTICS_TIMELINESS_TEMPLATE
          }}
          dialogProps={{
            maskClosable: false,
            style: {
              minWidth: 480
            }
          }}
        />
      ),
      name: ONLINE_LOGISTICS_TIMELINESS_TEMPLATE,
      bodyRender: ({ field }) => {
        return (
          <LogisticsTimelinessTemplateColumnCpn
            name={`${field}.${ONLINE_LOGISTICS_TIMELINESS_TEMPLATE}`}
            validate={ONLINE_DELIVERY_TEMPLATE_VALIDATE}
          />
        );
      }
    },
    [ONLINE_ORIGIN]: {
      title: (
        <BatchActionHeader
          title="划线价"
          titleOnly={isErrorItems}
          component={OriginPriceField}
          onConfirm={onBatchSet}
          props={{
            name: `batch_${ONLINE_ORIGIN}`,
            targetName: ONLINE_ORIGIN,
            // 增加字段区分批量设置
            type: 'batch'
          }}
        />
      ),
      width: 300,
      name: ONLINE_ORIGIN,
      bodyRender: ({ field }) => {
        return (
          <OriginPriceField name={`${field}.${ONLINE_ORIGIN}`} validate={ONLINE_ORIGIN_VALIDATE} />
        );
      }
    },
    [ONLINE_CATEGORY]: {
      title: (
        <BatchActionHeader
          withPop
          title="商品类目"
          popTitle="商品类目"
          titleOnly={isErrorItems}
          component={CategoryBatchCpn}
          onConfirm={onBatchCategorySet}
          props={{
            name: `batch_${ONLINE_CATEGORY}`,
            targetName: ONLINE_CATEGORY,
            isCategoryFetching,
            categoryFailInfo,
            retry
          }}
        />
      ),
      width: 350,
      name: ONLINE_CATEGORY,
      bodyRender: ({ field }) => {
        return (
          <CategoryColumnCpn
            isCategoryCredentials={isCategoryCredentials}
            name={`${field}.${ONLINE_CATEGORY}`}
          />
        );
      }
    },
    [ONLINE_GOODS_NO]: {
      title: (
        <BatchActionHeader
          title={`商品${batchCodeText}`}
          titleOnly={isErrorItems}
          component={GoodsNoField}
          onConfirm={onBatchSet}
          props={{
            name: `batch_${ONLINE_GOODS_NO}`,
            targetName: ONLINE_GOODS_NO
          }}
        />
      ),
      width: 220,
      name: ONLINE_GOODS_NO,
      bodyRender: ({ field }) => {
        return (
          <Field
            name={`${field}.${ONLINE_GOODS_NO}`}
            component="InputField"
            placeholder="请输入"
            validate={ONLINE_GOODS_NO_VALIDATE}
          />
        );
      }
    },
    [ONLINE_DISTRIBUTION]: {
      title: (
        <BatchActionHeader
          title="配送方式"
          titleOnly={isErrorItems}
          component={DistributionBatchCpn}
          onConfirm={onBatchSet}
          props={{
            name: `batch_${ONLINE_DISTRIBUTION}`,
            targetName: ONLINE_DISTRIBUTION,
            shouldIgnoreChangingItem: item => {
              return isFenXiao(item.goodsType);
            }
          }}
        />
      ),
      width: 300,
      name: ONLINE_DISTRIBUTION,
      bodyRender: ({ field, goodsType }) => {
        if (isFenXiao(goodsType)) {
          return '-';
        }
        return <DistributionColumnCpn name={`${field}.${ONLINE_DISTRIBUTION}`} />;
      }
    },
    [ONLINE_AFTER_SALE]: {
      title: (
        <BatchActionHeader
          title="售后服务"
          titleOnly={isErrorItems}
          component={AfterSaleBatchCpn}
          onConfirm={onBatchSet}
          props={{
            name: `batch_${ONLINE_AFTER_SALE}`,
            targetName: ONLINE_AFTER_SALE,
            shouldIgnoreChangingItem: item => {
              return isFenXiao(item.goodsType);
            }
          }}
        />
      ),
      width: 300,
      name: ONLINE_AFTER_SALE,
      bodyRender: ({ field, leafCategoryId, goodsType }) => {
        if (isFenXiao(goodsType)) {
          return '-';
        }
        return (
          <AfterSaleColumnCpn
            leafCategoryId={leafCategoryId}
            name={`${field}.${ONLINE_AFTER_SALE}`}
          />
        );
      }
    },
    [ONLINE_BARCODE]: {
      title: (
        <BatchActionHeader
          title="商品条码"
          titleOnly={isErrorItems}
          component={BarcodeField}
          onConfirm={onBatchSet}
          props={{
            label: '商品条码：',
            name: `batch_${ONLINE_BARCODE}`,
            targetName: ONLINE_BARCODE,
            validate: ONLINE_BARCODE_VALIDATE
          }}
        />
      ),
      width: 300,
      name: ONLINE_BARCODE,
      bodyRender: ({ field }) => {
        return (
          <Field
            name={`${field}.${ONLINE_BARCODE}`}
            component="InputField"
            placeholder="请输入字母或数字"
            validate={ONLINE_BARCODE_VALIDATE}
          />
        );
      }
    },
    [ONLINE_START_SALE_NUM]: {
      title: (
        <BatchActionHeader
          title="起售数量"
          titleOnly={isErrorItems}
          component={SaleNumStartBatchCpn}
          onConfirm={onBatchSet}
          props={{
            name: `batch_${ONLINE_START_SALE_NUM}`,
            targetName: ONLINE_START_SALE_NUM,
            shouldIgnoreChangingItem: item => {
              return isFenXiao(item.goodsType);
            }
          }}
        />
      ),
      width: 300,
      name: ONLINE_START_SALE_NUM,
      bodyRender: ({ field, goodsType }) => {
        if (isFenXiao(goodsType)) {
          return '-';
        }
        return (
          <SaleNumStartColumnCpn
            name={`${field}.${ONLINE_START_SALE_NUM}`}
            validate={ONLINE_SALE_NUM_VALIDATE}
          />
        );
      }
    },
    [ONLINE_SHOW_STOCK]: {
      title: (
        <BatchActionHeader
          title="是否展示库存"
          titleOnly={isErrorItems}
          component={StockShowBatch}
          onConfirm={onBatchSet}
          props={{
            name: `batch_${ONLINE_SHOW_STOCK}`,
            targetName: ONLINE_SHOW_STOCK
          }}
        />
      ),
      width: 200,
      name: ONLINE_SHOW_STOCK,
      bodyRender: ({ field }) => {
        return <StockShowColumn name={`${field}.${ONLINE_SHOW_STOCK}`} />;
      }
    },
    [ONLINE_PREPARE_TIME]: {
      title: (
        <BatchActionHeader
          title="备货时间"
          titleOnly={isErrorItems}
          component={PrepareTimeBatch}
          helpDesc="若商品为多规格商品，设置备货时间后，商品下所有规格的备货时间都相同。"
          onConfirm={onBatchSet}
          props={{
            name: `batch_${ONLINE_PREPARE_TIME}`,
            targetName: ONLINE_PREPARE_TIME,
            shouldIgnoreChangingItem: item => {
              return isFenXiao(item.goodsType);
            }
          }}
        />
      ),
      width: 460,
      name: ONLINE_PREPARE_TIME,
      bodyRender: ({
        field,
        isNonSpec,
        itemSkuMarkAggregateModelList: prepareTimeList,
        goodsType
      }) => {
        if (isFenXiao(goodsType)) {
          return '-';
        }
        const showTip = !isNonSpec && prepareTimeList?.length > 1;
        return <PrepareTimeColumn name={`${field}.${ONLINE_PREPARE_TIME}`} props={{ showTip }} />;
      }
    },
    [ONLINE_GOODS_INDEX]: {
      title: (
        <BatchActionHeader
          title="序号"
          titleOnly={isErrorItems}
          component={GoodsIndexBatchCpn}
          onConfirm={onBatchSet}
          props={{
            name: `batch_${ONLINE_GOODS_INDEX}`,
            targetName: ONLINE_GOODS_INDEX
          }}
        />
      ),
      width: 460,
      name: ONLINE_GOODS_INDEX,
      bodyRender: ({ field }) => {
        return <GoodsIndexColumnCpn name={`${field}.${ONLINE_GOODS_INDEX}`} />;
      }
    },
    [ONLINE_SALE_TYPE]: {
      title: (
        <BatchActionHeader
          title="售卖方式"
          titleOnly={isErrorItems}
          component={SaleTypeBatchCpn}
          onConfirm={onBatchDialogSet}
          withDialog
          props={{
            name: `batch_${ONLINE_SALE_TYPE}`,
            targetName: ONLINE_SALE_TYPE,
            shouldIgnoreChangingItem: item => {
              return isFenXiao(item.goodsType);
            },
            extraData: getPreSaleExtraData(hasHaitao)
          }}
        />
      ),
      width: 340,
      name: ONLINE_SALE_TYPE,
      bodyRender: ({ field, goodsType, bizMarkCode }) => {
        if (isFenXiao(goodsType)) {
          return '-';
        }
        return (
          <SaleTypeColumnCpn
            name={`${field}.${ONLINE_SALE_TYPE}`}
            validate={ONLINE_SALE_TYPE_VALIDATE}
            extraData={getPreSaleExtraData(isHaitao(bizMarkCode))}
          />
        );
      }
    },
    [ONLINE_PRODUCTION_TIME]: {
      title: (
        <BatchActionHeader
          title="加工时长"
          titleOnly={isErrorItems}
          component={ProductionTimeField}
          onConfirm={onBatchSet}
          props={{
            name: `batch_${ONLINE_PRODUCTION_TIME}`,
            targetName: ONLINE_PRODUCTION_TIME,
            shouldIgnoreChangingItem: item => {
              return checkIfCanNotEditOnlineProductionTime(item);
            },
            validateBatchChangingValue: value => {
              return validateProductionTime(value);
            },
            label: null
          }}
        />
      ),
      width: 400,
      name: ONLINE_PRODUCTION_TIME,
      bodyRender: item => {
        return (
          <ProductionTimeField
            name={`${item.field}.${ONLINE_PRODUCTION_TIME}`}
            label={null}
            disabled={checkIfCanNotEditOnlineProductionTime(item)}
          />
        );
      }
    },
    [ONLINE_CLASSIFICATION]: {
      title: (
        <BatchActionHeader
          title="商品分类"
          titleOnly={isErrorItems}
          component={GoodsClassificationBatchCpn}
          onConfirm={onBatchDialogSet}
          withDialog
          props={{
            name: `batch_${ONLINE_CLASSIFICATION}`,
            targetName: ONLINE_CLASSIFICATION
          }}
        />
      ),
      name: ONLINE_CLASSIFICATION,
      bodyRender: ({ field }) => {
        return (
          <GoodsClassificationColumnCpn
            columnType="goods"
            name={`${field}.${ONLINE_CLASSIFICATION}`}
          />
        );
      }
    },
    [ONLINE_BRAND]: {
      title: (
        <BatchActionHeader
          title="商品品牌"
          titleOnly={isErrorItems}
          component={GoodsBrandBatchCpn}
          onConfirm={onBatchDialogSet}
          withDialog
          props={{
            name: `batch_${ONLINE_BRAND}`,
            targetName: ONLINE_BRAND
          }}
        />
      ),
      name: ONLINE_BRAND,
      bodyRender: ({ field }) => {
        return <GoodsBrandColumnCpn columnType="goods" name={`${field}.${ONLINE_BRAND}`} />;
      }
    },
    [ONLINE_HEAVY_CONTINUED]: {
      title: (
        <BatchActionHeader
          title="续重收费"
          titleOnly={isErrorItems}
          component={HeavyContinuedField}
          onConfirm={onBatchSet}
          props={{
            name: `batch_${ONLINE_HEAVY_CONTINUED}`,
            targetName: ONLINE_HEAVY_CONTINUED,
            label: '续重收费：'
          }}
        />
      ),
      name: ONLINE_HEAVY_CONTINUED,
      bodyRender: ({
        field,
        isNonSpec,
        itemMeasModel,
        isMultiUnit,
        stocks = [],
        itemSkuMarkAggregateModelList = [],
        ...props
      }) => {
        const getHasWeight = () => {
          if (isMultiUnit || !isNonSpec) {
            const [, ...units] = stocks;
            return (
              /** 选择商品场景 */
              units.length === itemMeasModel?.itemMeasList?.length ||
              /** 商品导入场景 */
              itemSkuMarkAggregateModelList?.length === itemMeasModel?.itemMeasList?.length
            );
          }

          return itemMeasModel?.itemWeight > 0;
        };

        if (!props[ONLINE_DISTRIBUTION]?.cityDelivery || !getHasWeight()) {
          return '-';
        }
        return <HeavyContinuedField name={`${field}.${ONLINE_HEAVY_CONTINUED}`} />;
      }
    }
  };

  const MEITUAN_GOODS_COLUMN = {
    [MEITUAN_GOODS_GROUP]: {
      title: (
        <BatchActionHeader
          title="商品分组"
          titleOnly={isErrorItems}
          component={ExternalGroupField}
          onConfirm={onBatchSet}
          props={{
            name: `batch_${MEITUAN_GOODS_GROUP}`,
            targetName: MEITUAN_GOODS_GROUP,
            label: null,
            channel: SalesChannelEnum.MtWmChannelId
          }}
        />
      ),
      width: 400,
      name: MEITUAN_GOODS_GROUP,
      bodyRender: item => {
        return (
          <ExternalGroupField
            name={`${item.field}.${MEITUAN_GOODS_GROUP}`}
            label={null}
            channel={SalesChannelEnum.MtWmChannelId}
            validate={[
              {
                type: 'required',
                message: '请选择商品分组'
              }
            ]}
          />
        );
      }
    }
  };

  const ELEME_GOODS_COLUMN = {
    [ELEME_GOODS_GROUP]: {
      title: (
        <BatchActionHeader
          title="商品分组"
          titleOnly={isErrorItems}
          component={ExternalGroupField}
          onConfirm={onBatchSet}
          props={{
            name: `batch_${ELEME_GOODS_GROUP}`,
            targetName: ELEME_GOODS_GROUP,
            label: null,
            channel: SalesChannelEnum.ElemeChannelId
          }}
        />
      ),
      width: 400,
      name: ELEME_GOODS_GROUP,
      bodyRender: item => {
        return (
          <ExternalGroupField
            name={`${item.field}.${ELEME_GOODS_GROUP}`}
            label={null}
            channel={SalesChannelEnum.ElemeChannelId}
            validate={[
              {
                type: 'required',
                message: '请选择商品分组'
              }
            ]}
          />
        );
      }
    }
  };

  const MEITUAN_SHANGOU_GOODS_COLUMN = {
    [MEITUAN_SHANGOU_GOODS_GROUP]: {
      title: (
        <BatchActionHeader
          title="商品分组"
          titleOnly={isErrorItems}
          component={ShangouExternalGroupField}
          onConfirm={onBatchSet}
          props={{
            name: `batch_${MEITUAN_SHANGOU_GOODS_GROUP}`,
            targetName: MEITUAN_SHANGOU_GOODS_GROUP,
            label: null,
            channel: SalesChannelEnum.MtShanGouChannelId
          }}
        />
      ),
      width: 400,
      name: MEITUAN_SHANGOU_GOODS_GROUP,
      bodyRender: item => {
        return (
          <ShangouExternalGroupField
            name={`${item.field}.${MEITUAN_SHANGOU_GOODS_GROUP}`}
            label={null}
            channel={SalesChannelEnum.MtShanGouChannelId}
            validate={[
              {
                type: 'required',
                message: '请选择商品分组'
              }
            ]}
          />
        );
      }
    }
  };

  const COMPLETE_GOODS_COLUMN = {
    ...STORAGE_GOODS_COLUMN,
    ...OFFLINE_GOODS_COLUMN,
    ...ONLINE_GOODS_COLUMN,
    ...MEITUAN_GOODS_COLUMN,
    ...ELEME_GOODS_COLUMN,
    ...MEITUAN_SHANGOU_GOODS_COLUMN
  };

  if (get(fields, 'length', 0) === 0) {
    return [];
  }

  const idx = fields.indexOf(SPU_SELL_CHANNEL);

  const getFields = () => {
    if (idx === -1) {
      return fields;
    }
    return [
      ...fields.slice(0, idx),
      ...(HasOfflineAbility ? [OFFLINE_CHANNEL] : []),
      ONLINE_CHANNEL,
      ...fields.slice(idx + 1)
    ];
  };

  return getFields().map(field => {
    return COMPLETE_GOODS_COLUMN[field];
  });
};

export default getColumns;
