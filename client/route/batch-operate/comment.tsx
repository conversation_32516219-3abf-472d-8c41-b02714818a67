/* eslint-disable react/jsx-props-no-spreading */
import React, { useCallback, useState, useEffect } from 'react';
import {
  Form,
  FormControl,
  FieldSet,
  FieldUtils,
  Select,
  Input,
  Checkbox,
  ISelectProps,
  IInputCoreProps,
  ICheckboxProps,
  IInputChangeEvent,
  ICheckboxEvent,
  FormDescription
} from 'zent';

import { CommentType, ICommentProps, CommentData } from './types';

const { useFieldArray, useField, useFieldValue } = Form;

const CommentTypes = [
  { text: '文本', key: CommentType.Text },
  { text: '数字', key: CommentType.Number },
  { text: '邮箱', key: CommentType.Email },
  { text: '日期', key: CommentType.Date },
  { text: '身份证', key: CommentType.IdCard },
  { text: '图片', key: CommentType.Image },
  { text: '手机号', key: CommentType.Mobile },
  { text: '时间', key: CommentType.Time }
];

export const MaxTitleLength = 10;
export const MaxCommentsLength = 10;
export const CommentName = 'comment';

const SelectField: React.FC<ISelectProps<CommentType, typeof CommentTypes[number]>> = function (
  props
) {
  const model = Form.useField<CommentType | null>('type', null);
  const handleSelectChange = FieldUtils.useMulti(
    useCallback(() => {
      model.isTouched = true;
    }, [model]),
    FieldUtils.makeChangeHandler(model, Form.ValidateOption.Default)
  );
  const [selected, setSelected] = useState<typeof CommentTypes[number] | null>(null);
  useEffect(() => {
    const [selected] = CommentTypes.filter((type) => type.key === model.value);
    setSelected(selected ?? null);
  }, [model.value]);
  return (
    <Select
      {...props}
      width={180}
      multiple={false}
      inline
      value={selected}
      onChange={(value) => handleSelectChange(value ? value.key : null)}
    />
  );
};

const InputField: React.FC<IInputCoreProps> = function (props) {
  const model = useField('title', '');
  const handleInputChange = FieldUtils.useMulti(
    useCallback(() => {
      model.isTouched = true;
    }, [model]),
    FieldUtils.usePipe(
      (ev: IInputChangeEvent) => ev.target.value,
      FieldUtils.makeChangeHandler(model, Form.ValidateOption.Default)
    )
  );
  return <Input {...props} value={model.value} onChange={handleInputChange} />;
};

const CheckboxField: React.FC<ICheckboxProps<boolean> & { field: string }> = function ({
  children,
  field,
  ...props
}) {
  const model = useField(field, false);
  const handleChecked = FieldUtils.useMulti(
    useCallback(() => {
      model.isTouched = true;
    }, [model]),
    FieldUtils.usePipe(
      (ev: ICheckboxEvent<boolean>) => ev.target.checked,
      FieldUtils.makeChangeHandler(model, Form.ValidateOption.Default)
    )
  );
  return (
    <Checkbox {...props} checked={model.value} onChange={handleChecked}>
      {children}
    </Checkbox>
  );
};

function useHandleFieldModel(
  multipleVisible: boolean,
  timeVisible: boolean,
  typeValue: CommentType | null
) {
  const multipleModel = useField('multiple', false);
  const timeModel = useField('includeDate', false);
  const handleMultipleFieldChange = FieldUtils.useMulti(
    useCallback(() => {
      multipleModel.isTouched = true;
    }, [multipleModel]),
    FieldUtils.makeChangeHandler(multipleModel, Form.ValidateOption.IncludeUntouched)
  );
  const handleTimeFieldChange = FieldUtils.useMulti(
    useCallback(() => {
      timeModel.isTouched = true;
    }, [timeModel]),
    FieldUtils.makeChangeHandler(timeModel, Form.ValidateOption.IncludeUntouched)
  );
  useEffect(() => {
    if (!multipleVisible) {
      handleMultipleFieldChange(false);
    }
    if (!timeVisible) {
      handleTimeFieldChange(false);
    }
  }, [handleMultipleFieldChange, handleTimeFieldChange, multipleVisible, timeVisible, typeValue]);
}

const CommentItem: React.FC<{
  inputProps?: IInputCoreProps;
  onDelete: () => void;
  disabled?: boolean;
}> = function CommentItem({ inputProps = {}, onDelete, disabled }) {
  const typeValue = useFieldValue<CommentType>('type');

  const multipleVisible = typeValue === CommentType.Text;
  const timeVisible = typeValue === CommentType.Time;

  useHandleFieldModel(multipleVisible, timeVisible, typeValue);

  return (
    <>
      <SelectField
        className="gd-comment__input select"
        options={CommentTypes}
        popupWidth={180}
        inline
        disabled={disabled}
      />
      留言标题为&nbsp;&nbsp;
      <InputField disabled={disabled} className="gd-comment__input" inline {...inputProps} />
      <CheckboxField disabled={disabled} className="gd-comment__input" field="required">
        必填
      </CheckboxField>
      {multipleVisible && (
        <CheckboxField disabled={disabled} className="gd-comment__input" field="multiple">
          多行
        </CheckboxField>
      )}
      {timeVisible && (
        <CheckboxField disabled={disabled} className="gd-comment__input" field="includeDate">
          含日期
        </CheckboxField>
      )}
      {!disabled && (
        <div aria-label="button" className="gd-comment__btn" onClick={onDelete}>
          删除
        </div>
      )}
    </>
  );
};

const Comment: React.FC<ICommentProps> = function Comment({
  name = CommentName,
  label = '留言：',
  required = false,
  max = MaxTitleLength,
  helpDesc = `购买商品时让买家输入留言，最多可设置 ${max} 条留言。`,
  className,
  disabled
}) {
  const comments = useFieldArray<CommentData, any /* ModelRef */>(name);

  return (
    <FormControl className={className} label={label} required={required}>
      {comments.children.map((childModel, index) => (
        <div className="gd-comment__group" key={childModel.id}>
          <FieldSet model={childModel}>
            <CommentItem
              inputProps={{
                maxLength: max
              }}
              onDelete={() => comments.splice(index, 1)}
              disabled={disabled}
            />
          </FieldSet>
        </div>
      ))}
      {comments.children.length < MaxCommentsLength && !disabled && (
        <div
          aria-label="button"
          className="gd-comment__btn add"
          onClick={() =>
            comments.children.length <= MaxCommentsLength &&
            comments.push({
              title: `留言 ${comments.children.length + 1}`,
              type: CommentType.Text,
              multiple: false,
              required: false,
              includeDate: false
            })
          }
        >
          添加字段
        </div>
      )}
      <FormDescription>{helpDesc}</FormDescription>
    </FormControl>
  );
};

export default Comment;
