import { Space } from '@youzan/react-components';
import { PopInfo } from '@youzan/retail-components';
import { global } from '@youzan/retail-utils';
import {
  isHqStore,
  isPartnerStore,
  isRetailSingleStore,
  isUnifiedHqStore,
  isUnifiedOfflineBranchStore
} from '@youzan/utils-shop';
import {
  HasMultiChannelProductManageAbility,
  IsUnityModelRss,
  IsUnityModelUnifiedShop,
  IsUnityModelWithoutFwh,
  StoreStockSyncMode,
  SubShopStockSyncMode,
  ViewType
} from 'common/constants';
import BatchChannelStock from 'cpn/batch-channel-stock';
import { VipPriceBatchNewButton } from 'cpn/vip-price-button';
import { UseNewVipPriceBtn } from 'cpn/vip-price-button/helper';
import * as React from 'react';
import * as keys from 'shared/keys/shop-config';
import { BatchOperateType } from 'common/types';
import { PriceDialog } from './price';
import { Shelf, ShelfType } from './shelf';
import { StockNumDialog } from './stock-num';
import s from './style.scss';
import { trackOperateGoods } from './log';
import TableBatchWaySelect from 'cpn/table-batch-way-select';
import { isHqStoreAndNewModel } from 'common/constants/data-permissions';

interface IBatchProps {
  viewType: ViewType;
  goodsList?: any[];
  enableTakeUp?: boolean;
  enableTaskDown?: boolean;
  onBatchOperationSuccess?: () => void;
}

// 新模型-连锁总部支持改库存: 有一个门店没有使用进出存管理
export const allowEditUnityModelHqOfflineStock = IsUnityModelUnifiedShop && isUnifiedHqStore;

const {
  [keys.retailOrderManagerStock]: retailOrderManagerStock,
  allowEditRssOfflineStock = false
} = global.BUSINESS;

export function Batch({
  viewType,
  goodsList,
  enableTakeUp,
  enableTaskDown,
  onBatchOperationSuccess
}: IBatchProps) {
  const itemIds = goodsList.map(goods => goods.itemId);
  const subKdtIds = goodsList.map(goods => goods.kdtId);
  const vipDataList = goodsList.map(goods => ({
    itemId: goods.parentItemId,
    kdtId: goods.kdtId,
    channel: 1
  }));
  const shelfItems: ShelfType[] = [];
  enableTakeUp && shelfItems.push(ShelfType.On);
  enableTaskDown && shelfItems.push(ShelfType.Off);

  const allowEditStock = React.useMemo(() => {
    if (
      (HasMultiChannelProductManageAbility && !isPartnerStore) ||
      (IsUnityModelRss && allowEditRssOfflineStock)
    ) {
      return {
        showStockDialog: false,
        showStockDrawer: true
      };
    }
    // 非多渠道
    return {
      showStockDialog:
        !HasMultiChannelProductManageAbility &&
        [
          // 老模型除了供货模式
          !IsUnityModelWithoutFwh &&
            !(isUnifiedOfflineBranchStore && SubShopStockSyncMode.Supply === StoreStockSyncMode),
          // 新模型总部
          isUnifiedHqStore,
          // 新模型门店库存模式未使用进出存
          isUnifiedOfflineBranchStore && !Number(retailOrderManagerStock),
          // 零售单店来库存模式是独立销售/共享门店库存但未启用进出存
          IsUnityModelRss && allowEditRssOfflineStock
        ].some(flag => flag),
      showStockDrawer: false
    };
  }, []);

  const onBatchChannelStockSuccess = () => {
    onBatchOperationSuccess?.();
    trackOperateGoods(BatchOperateType.Stock);
  };

  return (
    <div className={s.batch}>
      <Space size="small">
        {isHqStoreAndNewModel ? (
          <TableBatchWaySelect />
        ) : (
          <>
            <p>当页全选</p>
            {goodsList?.length > 0 && <p className="has-selected">已选商品 {goodsList.length}</p>}
          </>
        )}
        {shelfItems.map(type => (
          <Shelf
            key={type}
            type={type}
            itemIds={itemIds}
            subKdtIds={subKdtIds}
            viewType={viewType}
            goodsList={goodsList}
            onConfirmSuccess={onBatchOperationSuccess}
          />
        ))}
        <PriceDialog
          itemIds={itemIds}
          goodsList={goodsList}
          subKdtIds={subKdtIds}
          onConfirmSuccess={onBatchOperationSuccess}
        />
        {allowEditStock.showStockDialog && (
          <StockNumDialog
            itemIds={itemIds}
            subKdtIds={subKdtIds}
            goodsList={goodsList}
            onConfirmSuccess={onBatchOperationSuccess}
          />
        )}
        {allowEditStock.showStockDrawer && (
          <BatchChannelStock
            goodsList={goodsList}
            onConfirmSuccess={onBatchChannelStockSuccess}
            skuStockType={isRetailSingleStore ? 'offlineSkuStock' : 'shopSkuStock'}
          />
        )}
        {UseNewVipPriceBtn && IsUnityModelUnifiedShop && (
          <VipPriceBatchNewButton
            data={vipDataList}
            onSubmitCallback={onBatchOperationSuccess}
            showCustomColumns={isHqStore || isPartnerStore}
          />
        )}
        {(isHqStore || IsUnityModelRss) && (
          <PopInfo popContent="更多批量操作可进入【商品库】操作。" position="top-left" />
        )}
      </Space>
    </div>
  );
}
