import * as React from 'react';
import { useEffect, useState, useMemo } from 'react';
import { omit, snakeCase } from 'lodash';
import { Grid, Tabs, IGridColumn, Alert } from 'zent';
import { Space, BlankLink } from '@youzan/react-components';
import { PageBlock } from '@youzan/retail-components';
import { useFetchTable } from '@youzan/hooks';
import { sumByWidth, setUrlDomain } from '@youzan/retail-utils';
import { isUnifiedHqStore, isBranchStore } from '@youzan/utils-shop';
import qs from 'query-string';
import { BIZ_ENUM, ShopCertNotice, getCertNoticeStatus } from '@youzan/shop-cert';
import { SellType } from '@youzan/zan-hasaki';

import { withIntro } from 'cpn/with-intro';
import {
  IsUnityModelUnifiedShop,
  HasLiteAbility,
  HasMultiChannelProductManageAbility,
  getViewType,
  IsUnityModelRss,
  IsOpenSelfBuild,
  onlyHasSingleChannel
} from 'common/constants';
import { ShopTabs } from 'cpn/channel-tabs';
import { OfflineGoodsUrl } from 'cpn/channel-tabs/constants';
import { RouteProps } from 'react-router-dom';
import { getRequestGoodsList } from 'common/api';
import { ListFilter } from './filter';
import { Operation } from './operation';
import { Batch } from './batch';
import { getColumns } from './column';
import { saleStatusTabs, InnerSaleStatus, innerSaleStatusValueMap } from '../constant';
import { ListQueryItem, searchUrl } from '../api';
import { IntroOptions } from './intro';
import { canSetCustomVipPrice } from '../bizs';
import { getRssColumns } from './rss-columns';
import {
  BatchModifySelectEnum,
  ListBatchContext,
  useBatchTableSelect
} from 'cpn/table-batch-way-select';
import { isHqStoreAndNewModel } from 'common/constants/data-permissions';

interface IProps {
  startIntro: () => void;
}

const { Container } = PageBlock;

/** 引导交互本地存储 KEY */
const LocalKey = 'UNITY_MODEL_STORE_LIST_INTRO';

const viewType = getViewType();

const SoldOutQsToTab = new Map([
  [0, InnerSaleStatus.OnSale],
  [1, InnerSaleStatus.SoldOut]
]);

const AdvancedList: React.FC<RouteProps & IProps> = ({ startIntro, location: { search } = {} }) => {
  const [activeId, setActiveId] = useState(
    SoldOutQsToTab.get(+qs.parse(search).isSoldOut) ?? InnerSaleStatus.Total
  );
  const [selectedItemIds, setSelectedItemIds] = useState<string[]>([]);
  const [alertShow, setAlertShow] = useState<{ cert: boolean }>({ cert: false });

  const {
    currentModify,
    setCurrentModify,
    inverseSelectData,
    setInverseSelectData
  } = useBatchTableSelect();
  const buildComboParams = (sellType: number) => {
    if (IsUnityModelUnifiedShop) {
      if (sellType === SellType.Combine) {
        return {
          notExcludeAbilityMarks: [10033],
          includeAbilityMarks: [10033]
        };
      }
      if (sellType === SellType.Single) {
        return {};
      }
      return {
        notExcludeAbilityMarks: [10033]
      };
    }
    return {};
  };

  const { items, filterProps, tableProps, reload } = useFetchTable(getRequestGoodsList(searchUrl), {
    shouldInitFetch: false,
    params: {
      activeId
    },
    handleParams: (value: any) => {
      return {
        ...omit(value, 'pageNo', 'activeId', 'sortBy', 'sortType', 'comboSellType'),
        ...innerSaleStatusValueMap.get(value.activeId),
        ...buildComboParams(value.comboSellType),
        order: value.sortType,
        orderBy: value.sortBy ? snakeCase(value.sortBy) : null,
        page: value.pageNo
      };
    }
  });

  const clearSelected = () => {
    setSelectedItemIds([]);
  };

  const onBatchOperationSuccess = () => {
    clearSelected();
    reload();
  };

  const handleTabsChange = (value: InnerSaleStatus) => {
    setActiveId(value);
    clearSelected();
  };

  const enableVipPrice = IsUnityModelUnifiedShop && canSetCustomVipPrice;

  /** （非多渠道 || 单渠道）& 分店 & 有自建能力时 => 有复制功能 */
  const showCopy =
    (!HasMultiChannelProductManageAbility ||
      (HasMultiChannelProductManageAbility && onlyHasSingleChannel)) &&
    IsUnityModelUnifiedShop &&
    isBranchStore &&
    IsOpenSelfBuild;

  let width = 70;
  [enableVipPrice, showCopy].forEach(item => {
    if (item) {
      width += 40;
    }
  });
  const columns: IGridColumn<ListQueryItem>[] = (IsUnityModelRss
    ? getRssColumns({ reload, activeId })
    : getColumns({ reload, activeId })
  ).concat({
    title: '操作',
    width,
    fixed: 'right',
    textAlign: 'right',
    bodyRender: data => (
      <Operation
        data={data}
        isVipPriceEnabled={enableVipPrice}
        reload={reload}
        showCopy={showCopy}
      />
    )
  });

  const scrollX = { x: sumByWidth(columns) };

  const storeAlert = useMemo(() => {
    const alertItems = [];

    /**
     * 店铺锁区公告
     * 当出现此公告时，屏蔽其他公告
     */
    if (alertShow.cert) {
      alertItems.push(<ShopCertNotice biz={BIZ_ENUM.GOODS_STORE} />);
      return alertItems;
    }

    alertItems.push(
      <Alert>
        <span>
          现支持在门店关闭进出存的情况下，给网店进行供货。可直接在门店商品列表修改库存，同时给网店同步库存。详情可查看
        </span>
        <BlankLink
          href={setUrlDomain(
            '/forum.php?mod=viewthread&tid=683783&page=1&extra=#pid3889381',
            'bbs'
          )}
        >
          操作说明
        </BlankLink>
      </Alert>
    );

    /** 交互规范，最多只展示两条公告 */
    return alertItems.splice(0, 2);
  }, [alertShow]);

  const loadCertStatus = () => {
    getCertNoticeStatus(BIZ_ENUM.GOODS_STORE).then(({ isShow }) => {
      setAlertShow(preState => ({
        ...preState,
        cert: isShow
      }));
    });
  };

  useEffect(() => {
    startIntro();
    loadCertStatus();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const onSelect = (_selected: string[]) => {
    setSelectedItemIds(_selected);

    if (isHqStoreAndNewModel) {
      if (currentModify === BatchModifySelectEnum.MODIFY_ALL_GOODS) {
        // const selectedIds = _selected.map(item => Number(item.split('_')[1]));
        // 获取当前页的itemIds
        const curPageItemIds = (items as any[]).map(item => item.itemId);
        // 获取当前页选中的itemIds
        const curPageSelectItemIds = _selected.filter(itemId => curPageItemIds.includes(itemId));
        const inverseItemIdData = (items as any[])
          .filter(item => !curPageSelectItemIds.includes(item.itemId))
          .map(item => item.itemId);

        // 将其他页未选中的数据和当前页未选中的数据合并
        setInverseSelectData([
          ...inverseSelectData.current.filter(itemId => !curPageItemIds.includes(itemId)),
          ...inverseItemIdData
        ]);
      }
    }
  };

  return (
    <ListBatchContext.Provider
      value={{
        total: tableProps.pageInfo.total,
        onModifyWayChange: (key: BatchModifySelectEnum) => {
          setCurrentModify(key);
          clearSelected();
        }
      }}
    >
      <Container>
        {HasMultiChannelProductManageAbility && (
          <ShopTabs showChannelTab channelActiveId={OfflineGoodsUrl} />
        )}
        <Space direction="vertical">
          <div>{storeAlert}</div>
          <ListFilter
            {...filterProps}
            viewType={viewType}
            innerSaleStatus={activeId}
            clearSelected={clearSelected}
          />
          <Tabs
            className="intro-tabs-scroll"
            type="card"
            tabs={saleStatusTabs}
            activeId={activeId}
            onChange={handleTabsChange}
          />
          <Grid
            rowKey="itemId"
            datasets={items}
            {...tableProps}
            columns={columns}
            selection={{
              selectedRowKeys: selectedItemIds,
              onSelect
            }}
            scroll={scrollX}
            batchRender={goodsList => (
              <Batch
                viewType={viewType}
                goodsList={goodsList}
                enableTakeUp={[InnerSaleStatus.Total, InnerSaleStatus.OffSale].includes(activeId)}
                enableTaskDown={[
                  InnerSaleStatus.Total,
                  InnerSaleStatus.OnSale,
                  InnerSaleStatus.SoldOut
                ].includes(activeId)}
                onBatchOperationSuccess={onBatchOperationSuccess}
              />
            )}
          />
        </Space>
      </Container>
    </ListBatchContext.Provider>
  );
};

export default withIntro(
  LocalKey,
  IntroOptions,
  AdvancedList,
  /** 页面的引导提示只针对专业版 和 零售单店 升级的商家 */
  (IsUnityModelUnifiedShop && isUnifiedHqStore && !HasLiteAbility) || IsUnityModelRss
);
