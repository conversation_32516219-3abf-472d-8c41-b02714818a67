import { global } from '@youzan/retail-utils';
import { LifecycleType, GoodsType, SellChannel } from '@youzan/zan-hasaki';
import { EnumMarkCode } from '@youzan/goods-domain-definitions';
import { isBranchStore } from '@youzan/utils-shop';

import {
  HasMultiChannelProductManageAbility,
  IsUnityModeWithoutLite,
  IsUnityModelWithoutFwh
} from 'common/constants';
import { IBusiness } from 'common/types';
import { SalesChannelEnum } from '@youzan/goods-domain-pc-components';

import { ProductIdDTO } from './dto';

const { validLifeCycleList = [] } = global.BUSINESS as IBusiness;
const { KDT_ID } = global;

/**
 * 历史商品迁移标识
 */
export enum ProdItemMigrationTag {
  /** 历史商品待迁移 */
  Pending = 1,
  /** 历史商品已迁移 */
  Done,
  /** 重建商品 */
  Rebuilt,
  /** 该类型商品被删除时，有与其关联的商品会同时被删除 */
  Related,
  /** 在原商品上重建的商品 */
  RebuiltSelf
}

/** 历史商品级联下拉框筛选项value枚举 */
export enum MigrationStatusOptionsValue {
  Pending = ProdItemMigrationTag.Pending,
  Done = ProdItemMigrationTag.Done,
  Rebuilt = ProdItemMigrationTag.Rebuilt,
  /** 考虑以后可能会新增筛选value，避免冲突，历史商品一级value从99开始 */
  Legacy = 99,
  All
}

// 商品详情页筛选
export const goodsDetailOptions = [
  { value: -1, text: '全部' },
  { value: 0, text: '未传详情页' },
  { value: 1, text: '已传详情页' }
];

// 商品图片筛选
export const goodsImageOptions = [
  { value: -1, text: '全部' },
  { value: 0, text: '未传图' },
  { value: 1, text: '已传图' }
];

export const GoodsTypeOptions = [
  { value: -99, text: '全部' },
  { value: GoodsType.Actual, text: '实物商品' },
  { value: GoodsType.Virtual, text: '虚拟商品' },
  { value: GoodsType.Digital, text: '电子卡券' },
  { value: GoodsType.Cake, text: '茶饮烘培' },
  { value: GoodsType.FenXiao, text: '分销商品' },
  { value: GoodsType.Coupon, text: '付费优惠券' }
];

export const stockDeductModeOptions = [
  {
    text: '全部',
    value: -1
  },
  {
    text: '拍下扣减库存',
    value: 0
  },
  {
    text: '付款扣减库存',
    value: 1
  }
];

export const defaultLifecycleTypes = validLifeCycleList.map(option => option.type);

export const defaultFilters: IFilterValues = {
  nameOrCode: '',
  inventoryTypes: {
    text: '全部',
    value: null
  },
  categoryId: {
    text: '所有分类',
    value: -1
  },
  channelParam: {
    channelType: 3, // 默认选择全部
    channelList: [],
    text: '全部'
  },
  defaultVendorId: {
    text: '全部',
    value: null
  },
  brandId: {
    text: '全部',
    value: null
  },
  organizationId: isBranchStore
    ? {
        value: KDT_ID,
        text: global.BUSINESS.shopInfo?.shopName
      }
    : {
        text: '全部',
        value: null
      },
  categoryProperties: {
    text: '全部',
    value: [null]
  },
  itemPropertiesSearch: {
    text: '全部',
    value: null
  },
  deliveryType: [],
  preSaleTypes: [],
  itemTypes: GoodsTypeOptions.find(item => item.text === '全部'),
  containPicture: goodsImageOptions.find(item => item.text === '全部'),
  containContent: goodsDetailOptions.find(item => item.text === '全部'),
  lifecycleIds: IsUnityModelWithoutFwh
    ? []
    : defaultLifecycleTypes.filter(item => item !== LifecycleType.Obsoleted),
  /** 异常商品，默认查全部 */
  prodItemMigrationTag: [MigrationStatusOptionsValue.All],
  stockDeductMode: stockDeductModeOptions.find(item => item.text === '全部'),
  multiCode: {
    selectValue: 'spuCodeMatch',
    inputValue: ''
  },
  allChannelGroup: {
    groupChannel: null,
    secondGroupIds: [],
    groupText: ''
  },
  price: [],
  salesShopParam: {
    salesShopType: 3,
    selectedShop: []
  },
  saleStatusParam: []
};

/**
 * value 对应后端搜索字段
 */
export enum DeliveryType {
  Express = 'express',
  CityDelivery = 'cityDelivery',
  SelfPick = 'selfPick',
  HeavyContinued = 'heavyContinued'
}

/**
 * key 对应后端返回字段，value 为 label
 */
export const deliveryTypeToLabel = new Map([
  ['express', '快递发货'],
  ['cityDelivery', '同城配送'],
  ['selfPick', '门店自提'],
  ['heavyContinued', '续重收费']
]);

export const deliveryTypeOptions = [
  { value: DeliveryType.Express, text: deliveryTypeToLabel.get(DeliveryType.Express) },
  { value: DeliveryType.CityDelivery, text: deliveryTypeToLabel.get(DeliveryType.CityDelivery) },
  { value: DeliveryType.SelfPick, text: deliveryTypeToLabel.get(DeliveryType.SelfPick) }
  // 根据 https://doc.qima-inc.com/pages/viewpage.action?pageId=321519913 99 条无此项
  // { value: DeliveryType.HeavyContinued, text: deliveryTypeToLabel.get(DeliveryType.HeavyContinued) }
];

export const preSaleTypeOptions = [
  { value: -1, text: '非预售' },
  { value: 0, text: '全款预售' },
  { value: 1, text: '定金预售' }
];

export const salesStatusOptions = [
  { value: 0, text: '销售中' },
  { value: 1, text: '已售罄' },
  { value: 2, text: '已下架' }
];

export const migrationStatusOptions = [
  {
    value: MigrationStatusOptionsValue.All,
    label: '全部商品'
  },
  {
    value: MigrationStatusOptionsValue.Rebuilt,
    label: '重建商品'
  },
  {
    value: MigrationStatusOptionsValue.Legacy,
    label: '历史商品',
    children: [
      {
        value: MigrationStatusOptionsValue.Pending,
        label: '未处理'
      },
      {
        value: MigrationStatusOptionsValue.Done,
        label: '已处理'
      }
    ]
  }
];

/**
 * @see https://doc.qima-inc.com/pages/viewpage.action?pageId=325443664#id-%E5%95%86%E5%93%81%E5%88%97%E8%A1%A8%E9%A1%B5%E6%8E%A5%E5%8F%A3%E6%96%87%E6%A1%A3-1.0.2%E5%95%86%E5%93%81%E7%B1%BB%E5%9E%8B
 */
export const GoodsTypeToReqPayloadMap = new Map([
  [
    null,
    {
      isVirtuals: [],
      itemTypes: [],
      bizCodes: [],
      notExcludeAbilityMarks: []
    }
  ],
  [
    GoodsType.Actual,
    {
      isVirtuals: [0],
      itemTypes: [0],
      notExcludeAbilityMarks: []
    }
  ],
  [
    GoodsType.Virtual,
    {
      isVirtuals: [2],
      itemTypes: [0],
      excludeAbilityMarks: [40011]
    }
  ],
  [
    GoodsType.Coupon,
    {
      isVirtuals: [2],
      itemTypes: [0],
      bizCodes: [EnumMarkCode.Coupon]
    }
  ],
  [
    GoodsType.Digital,
    {
      isVirtuals: [3],
      itemTypes: [0],
      notExcludeAbilityMarks: []
    }
  ],
  [
    GoodsType.Cake,
    {
      isVirtuals: [0],
      itemTypes: [0],
      bizCodes: ['010000000001'],
      notExcludeAbilityMarks: []
    }
  ],
  [
    GoodsType.FenXiao,
    {
      isVirtuals: [],
      itemTypes: [GoodsType.FenXiao],
      notExcludeAbilityMarks: []
    }
  ]
]);

export enum GoodsBatchUpdateChan {
  All,
  Online,
  Offline,
  MeiTuan,
  Eleme,
  ShanGou,
  Jd
}

export const GoodsBatchUpdateMap = new Map([
  [GoodsBatchUpdateChan.MeiTuan, SalesChannelEnum.MtWmChannelId],
  [GoodsBatchUpdateChan.Jd, SalesChannelEnum.JdChannelId],
  [GoodsBatchUpdateChan.Eleme, SalesChannelEnum.ElemeChannelId]
]);
export enum GoodsBatchUpdateType {
  // 增量更新
  Add = 1,
  // 替换更新
  Replace,
  // 删除更新
  Delete
}

// value 值各项对应 client/components/goods-batch-update/modules/index 中导出名称
export const GoodsBatchUpdateChanAbilityModel = new Map([
  [GoodsBatchUpdateChan.All, ['goodsName', 'vipDiscount', 'lifecycle']],
  [
    GoodsBatchUpdateChan.Online,
    [
      'goodsName',
      'sellTime',
      'purchaseQuota',
      'userQuota',
      'vipDiscount',
      'goodsTemplate',
      'goodsGroup',
      'goodsLabel',
      IsUnityModeWithoutLite || HasMultiChannelProductManageAbility ? 'stockDeduction' : ''
    ].filter(Boolean)
  ],
  [GoodsBatchUpdateChan.Offline, ['goodsName', 'vipDiscount', 'goodsGroup', 'goodsLabel']],
  [GoodsBatchUpdateChan.MeiTuan, ['takeoutPackingCharges', 'externalGroup']],
  [GoodsBatchUpdateChan.Eleme, ['takeoutPackingCharges', 'externalGroup']],
  [GoodsBatchUpdateChan.Jd, ['takeoutPackingCharges', 'externalGroupJd']],
  [GoodsBatchUpdateChan.ShanGou, ['takeoutPackingCharges', 'externalGroupShangou']]
]);

/** 渠道选择器的options */
export const channelOptions = {
  [SellChannel.Offline]: '商品渠道：门店',
  [SellChannel.Online]: '商品渠道：网店',
  [SellChannel.ELeMe]: '',
  [SellChannel.Jd]: '',
  [SellChannel.MeiTuan]: '',
  [SellChannel.MeiTuanShanGou]: ''
};

/** 商品goodsId转化获取 */
export const channelGoodsId: {
  [key: number]: keyof ProductIdDTO;
} = {
  [SellChannel.Offline]: 'offlineItemId',
  [SellChannel.Online]: 'onlineItemId'
};

/**
 * 历史商品重建任务状态
 */
export enum LegacyRebuildTaskStatus {
  Pending,
  Failed,
  Retry = 3,
  Doing = 5,
  Success = 10
}

export const REAL_GOODS = { value: 0, text: '实物商品', markCode: '000000000000' };
export const HOTEL_GOODS = { value: 35, text: '酒店商品' };
export const PAID_LEVEL_GOODS = { value: 91, text: '付费等级', markCode: '010000000050' };
export const COUPON_GOODS = { value: 92, text: '付费优惠券', markCode: '010000000051' };

export const TiktokChannel = 401;

export enum BillStatus {
  All = null,
  /** 待提交 */
  WaitingSubmit = 0,
  /** 待审核 */
  WaitingReview = 1,
  /** 已通过 */
  Resolved = 2,
  /** 已驳回 */
  Rejected = 3,
  /** 修改中 */
  Modifying = 6,
  /** 已删除 */
  Delete = 8
}

export const BillStatusTextMap = new Map([
  [BillStatus.WaitingSubmit, '待提交'],
  [BillStatus.WaitingReview, '待总部审核'],
  [BillStatus.Resolved, '已通过'],
  [BillStatus.Rejected, '已驳回'],
  [BillStatus.Modifying, '修改中'],
  [BillStatus.Delete, '已删除']
]);
