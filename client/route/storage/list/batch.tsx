/* eslint-disable react/jsx-props-no-spreading */
import * as React from 'react';
import { useMemo, useEffect, useState } from 'react';
import { Notify, IPublicCascaderItem, Alert } from 'zent';
import { VipPriceDrawer } from '@youzan/react-components';
import { isBranchStore, isFrontWarehouse, isHqStore, isSingleStore } from '@youzan/utils-shop';
import {
  ISpuSearchData,
  IClassification,
  InventoryType,
  GoodsType,
  SellChannel,
  ProductDisplayStatus
} from '@youzan/zan-hasaki';
import classnames from 'classnames';
import { LinkButton, GoodsUpgradeAlert } from '@youzan/retail-components';
import { checkAccess } from '@youzan/sam-components';
import { global } from '@youzan/retail-utils';
import { map } from 'lodash';
import {
  DeliveryType,
  SalesChannelEnum,
  SalesChannelEnumTextMap
} from '@youzan/goods-domain-pc-components';

import TableBatchCpn, { checkSelected } from 'cpn/table-batch-cpn';

import PopSelect from 'cpn/pop-select';
import { VipPriceBatchButton, VipPriceBatchNewButton } from 'cpn/vip-price-button';
import { ShowUpgradeNewVipPriceBtn } from 'cpn/vip-price-button/helper';
import { UseNewVipPriceBtn } from 'cpn/vip-price-button/helper';
import {
  GoodsChannel,
  IsUnityModelUnifiedShop,
  HasOfflineAbility,
  HasOfflineStoreGoodsManageAbility,
  HasSupplyChainAbility,
  HasLegacyAbility,
  OfflineStoreGoodsManageAbilityValidOrExpired,
  HasMultiChannelProductManageAbility,
  GoodsDisplayStatus,
  IsUnityModelRss,
  IsUnityModelWithoutFwh,
  HasMeiTuanChannelInMulti,
  HasElemeChannelInMulti,
  HasJdChannelInMulti,
  HasMeiTuanShanGouChannelInMulti
} from 'common/constants';
import { SubShopStatus } from 'common/types';
import { Release as BatchPublish, BatchUpdate, Rack, RssRack, BatchSale } from 'route/batch';

import { getSupportGoodsLabelSelected } from 'cpn/goods-batch-update/modules/goods-label';

import { BatchShelf } from './components/batch-shelf';
import BatchUpdateHeader from './components/batch-update/header';
import { DeleteAction, BatchRebuild } from './components';
import {
  GoodsBatchUpdateChan,
  GoodsBatchUpdateChanAbilityModel,
  ProdItemMigrationTag
} from './constants';
import StorageMoreActions from './components/more-actions';
import DeliveryTypeBtn from './components/delivery-type/button';
import GoodsAttrBtn from './components/goods-attr';
import ModifyPrices from './components/prices';
import ModifyStock from './components/stock';
import { isUnityModelData } from './helper';
import OfflineBatchRelease from './components/release';
import OnlineBatchRelease from './components/online-batch-release';
import ExternalBatchRelease from './components/external-waimai-release';
import { MultiChannelBatchPublish } from './components/batch-publish';
import {
  trackListBatchShelf,
  trackListBatchDelete,
  trackListBatchPrice,
  trackListBatchStock,
  trackListBatchVipPrice,
  trackListBatchDelivery,
  trackListBatchGoodsAttr,
  trackListBatchSetting,
  trackListBatchSale
} from './track';

import { ProductSearchDTO } from './dto';
import css from './batch.scss';
import RssModifyStock from './components/rss-batch-stock';
import RssOfflineBatchRelease from './components/rss-offline-batch-release';
import { queryTripChannelBind } from './api';
import BatchClassification from './components/batch-classification';

const { ShowCheckMsgDialog } = VipPriceDrawer;
const { VipPriceUpgrade } = GoodsUpgradeAlert;

interface IProps {
  datasets: ProductSearchDTO[] | ISpuSearchData[];
  setSelected: (selected: ProductSearchDTO[] | ISpuSearchData[]) => void;
  onSuccess: () => void;
  onRebuild: (itemIds: number[]) => void;
  selected: ProductSearchDTO[] | ISpuSearchData[];
  catList: IClassification[];
}

const { KDT_ID } = global;
const { inNewMemberPrice = false, disableBatchRelease = false } = global.BUSINESS;

/** 是否绑定外部渠道 */
export const getBindExternalStatus = (items: { bindedChannelIdList: number[] }[], id: number) => {
  return !!items.find(({ bindedChannelIdList = [] }: { bindedChannelIdList: number[] }) =>
    bindedChannelIdList.includes(id)
  );
};

const Batch: React.FC<IProps> = ({
  datasets,
  setSelected,
  selected,
  catList,
  onSuccess,
  onRebuild
}) => {
  const [bind, setBind] = useState({
    isBindMtWm: false,
    isBindEleme: false,
    isBindJd: false,
    isBindMtShanGou: false
  });

  useEffect(() => {
    // 新商品模型-非多渠道查询渠道绑定情况
    if (!HasMultiChannelProductManageAbility && IsUnityModelWithoutFwh) {
      queryTripChannelBind().then((items: any[]) => {
        setBind({
          isBindMtWm: getBindExternalStatus(items, SalesChannelEnum.MtWmChannelId),
          isBindEleme: getBindExternalStatus(items, SalesChannelEnum.ElemeChannelId),
          isBindJd: getBindExternalStatus(items, SalesChannelEnum.JdChannelId),
          isBindMtShanGou: getBindExternalStatus(items, SalesChannelEnum.MtShanGouChannelId)
        });
      });
    }
  }, []);

  const selectedWithoutLegacy = useMemo(
    () =>
      HasLegacyAbility
        ? (selected as ProductSearchDTO[]).filter(
            item =>
              !item.prodItemMigrationTag ||
              (item.prodItemMigrationTag !== ProdItemMigrationTag.Pending &&
                item.prodItemMigrationTag !== ProdItemMigrationTag.Done)
          )
        : selected,
    [selected]
  );
  const selectedSpuIds = useMemo(
    () =>
      map(selected, (item: ProductSearchDTO | ISpuSearchData) =>
        isUnityModelData(item) ? item.idSummary.spuId : item.spuId
      ).filter(Boolean),
    [selected]
  );
  const selectedOnlineItemIds = useMemo(
    () =>
      IsUnityModelWithoutFwh
        ? (selected as ProductSearchDTO[])
            .map(item => (item as ProductSearchDTO).idSummary.onlineItemId)
            .filter(Boolean)
        : [],
    [selected]
  );
  const selectedOfflineItemIds = useMemo(
    () =>
      IsUnityModelWithoutFwh
        ? (selected as ProductSearchDTO[])
            .map(item => (item as ProductSearchDTO).idSummary.offlineItemId)
            .filter(Boolean)
        : [],
    [selected]
  );
  const selectedLegacyItemIds = useMemo(
    () =>
      HasLegacyAbility
        ? (selected as ProductSearchDTO[])
            .filter(
              item =>
                item.prodItemMigrationTag === ProdItemMigrationTag.Pending ||
                item.prodItemMigrationTag === ProdItemMigrationTag.Done
            )
            .map(item => item.idSummary.onlineItemId)
        : [],
    [selected]
  );
  const selectedSpuIdsWithoutLegacy = useMemo(
    () =>
      HasLegacyAbility
        ? selectedWithoutLegacy
            .map(item => (isUnityModelData(item) ? item.idSummary.spuId : item.spuId))
            .filter(Boolean)
        : selectedSpuIds,
    [selectedWithoutLegacy, selectedSpuIds]
  );
  const selectedOnlineItemIdsWithoutLegacy = useMemo(
    () =>
      HasLegacyAbility
        ? (selectedWithoutLegacy as ProductSearchDTO[])
            .map(item => item.idSummary.onlineItemId)
            .filter(Boolean)
        : selectedOnlineItemIds,
    [selectedWithoutLegacy, selectedOnlineItemIds]
  );
  const selectedLegacyPendingItemIds = useMemo(
    () =>
      HasLegacyAbility
        ? (selected as ProductSearchDTO[])
            .filter(item => item.prodItemMigrationTag === ProdItemMigrationTag.Pending)
            .map(item => item.idSummary.onlineItemId)
        : [],
    [selected]
  );
  /** 这些商品被删除时会有与其关联的商品同时被删除 */
  const selectedDelRelated = useMemo(
    () =>
      HasLegacyAbility
        ? (selected as ProductSearchDTO[])
            .filter(item => item.prodItemMigrationTag === ProdItemMigrationTag.Related)
            .map((item: ProductSearchDTO | ISpuSearchData) => {
              return {
                spuId: isUnityModelData(item) ? item.idSummary.spuId : item.spuId,
                itemId: (item as ProductSearchDTO).idSummary.onlineItemId,
                sellChannel: SellChannel.Online
              };
            })
        : [],
    [selected]
  );
  const hasFenxiao = selected.some(
    (item: ProductSearchDTO & ISpuSearchData) => item.goodsType === GoodsType.FenXiao
  );

  const handleBatchUpdateComplete = (successList: number[]) => {
    if (successList?.length > 0) {
      onSuccess();
    }
  };

  /** 以下是新会员价组件的逻辑 */

  const goodsList = useMemo(() => {
    const res: any = [];
    selected.forEach(item => {
      if ((item as ProductSearchDTO)?.idSummary) {
        const {
          idSummary: { onlineItemId, offlineItemId },
          sellChannelSettings
        } = item as ProductSearchDTO;
        if (onlineItemId && sellChannelSettings.find(item => item.channel === SellChannel.Online)) {
          res.push({
            itemId: onlineItemId,
            kdtId: item.kdtId,
            channel: 0
          });
        }
        if (
          offlineItemId &&
          sellChannelSettings.find(item => item.channel === SellChannel.Offline)
        ) {
          res.push({
            itemId: offlineItemId,
            kdtId: item.kdtId,
            channel: 1
          });
        }
      }
    });
    return res;
  }, [selected]);

  const [visible, setVisible] = React.useState(false);
  const [validateData, setValidateData] = React.useState({});

  const openBatchVipDrawer = (data: any) => {
    setVisible(true);
    setValidateData(data);
  };

  /** 以上是新会员价组件的逻辑 */

  const rackOptions = [
    <Rack
      success={onSuccess}
      selected={[]}
      selectedData={selected as ProductSearchDTO[]}
      selectedOnlineItemIds={selectedOnlineItemIds}
      selectedOfflineItemIds={selectedOfflineItemIds}
      key="up-shelf"
      display={ProductDisplayStatus.OnShelves}
      onDialogConfirm={() => trackListBatchShelf({ onShelf: true })}
    >
      <div className={css.batch__popover__btn}>上架</div>
    </Rack>,
    <Rack
      success={onSuccess}
      selected={[]}
      selectedData={selected as ProductSearchDTO[]}
      selectedOnlineItemIds={selectedOnlineItemIds}
      selectedOfflineItemIds={selectedOfflineItemIds}
      key="down-shelf"
      display={ProductDisplayStatus.OffShelves}
      onDialogConfirm={() => trackListBatchShelf({ offShelf: true })}
    >
      <div className={css.batch__popover__btn}>下架</div>
    </Rack>,
    <Rack
      success={onSuccess}
      selected={[]}
      selectedData={selected as ProductSearchDTO[]}
      selectedOnlineItemIds={selectedOnlineItemIds}
      selectedOfflineItemIds={selectedOfflineItemIds}
      key="no-sale"
      display={GoodsDisplayStatus.NoSell}
      onDialogConfirm={() => trackListBatchShelf({ noSale: true })}
    >
      <div className={css.batch__popover__btn}>不可售</div>
    </Rack>
  ];

  // rss 的上下架 options
  const rssRackOptions = [
    <RssRack
      success={onSuccess}
      selected={[]}
      selectedData={selected as ProductSearchDTO[]}
      selectedOnlineItemIds={selectedOnlineItemIds}
      selectedOfflineItemIds={selectedOfflineItemIds}
      key="up-shelf"
      display={ProductDisplayStatus.OnShelves}
      goodsList={selected as ProductSearchDTO[]}
    >
      <div className={css.batch__popover__btn}>上架</div>
    </RssRack>,
    <RssRack
      success={onSuccess}
      selected={[]}
      selectedData={selected as ProductSearchDTO[]}
      selectedOnlineItemIds={selectedOnlineItemIds}
      selectedOfflineItemIds={selectedOfflineItemIds}
      key="down-shelf"
      display={ProductDisplayStatus.OffShelves}
      goodsList={selected as ProductSearchDTO[]}
    >
      <div className={css.batch__popover__btn}>下架</div>
    </RssRack>
  ].filter(Boolean);

  const batchUpdateProps = {
    selected: selectedSpuIds, // 兼容老组件
    selectedItems: selected as ProductSearchDTO[],
    success: onSuccess
  };

  const showBatchMeiTuan = HasMeiTuanChannelInMulti || bind.isBindMtWm;
  const showBatchEleme = HasElemeChannelInMulti || bind.isBindEleme;
  const showBatchJd = HasJdChannelInMulti || bind.isBindJd;
  const showBatchMeiTuanShanGou = HasMeiTuanShanGouChannelInMulti || bind.isBindMtShanGou;

  const releaseItems = [
    // lite 仅保留门店，https://doc.qima-inc.com/pages/viewpage.action?pageId=321519913 关键字，批量发布
    !IsUnityModelWithoutFwh && (
      <BatchPublish
        isStorage
        isOnline
        beforeShow={() => checkSelected(selectedSpuIds)}
        {...batchUpdateProps}
      />
    ),
    !IsUnityModelWithoutFwh && HasOfflineAbility && (
      <BatchPublish
        isStorage
        beforeShow={() => checkSelected(selectedSpuIds)}
        {...batchUpdateProps}
      />
    ),
    IsUnityModelWithoutFwh && (
      <OnlineBatchRelease
        {...batchUpdateProps}
        onSuccess={onSuccess}
        validateSelectedItems={checkSelected}
      />
    ),
    IsUnityModelUnifiedShop && HasOfflineStoreGoodsManageAbility && (
      <OfflineBatchRelease
        {...batchUpdateProps}
        onSuccess={onSuccess}
        validateSelectedItems={checkSelected}
      />
    ),
    IsUnityModelRss && HasOfflineStoreGoodsManageAbility && (
      <RssOfflineBatchRelease
        {...batchUpdateProps}
        onSuccess={onSuccess}
        validateSelectedItems={checkSelected}
      />
    ),
    showBatchMeiTuan && (
      <ExternalBatchRelease
        {...batchUpdateProps}
        onSuccess={onSuccess}
        validateSelectedItems={checkSelected}
        channelInfo={{
          text: SalesChannelEnumTextMap.get(SalesChannelEnum.MtWmChannelId),
          channel: SalesChannelEnum.MtWmChannelId
        }}
        samName={IsUnityModelRss ? '批量发布' : null}
      />
    ),
    showBatchEleme && (
      <ExternalBatchRelease
        {...batchUpdateProps}
        onSuccess={onSuccess}
        validateSelectedItems={checkSelected}
        channelInfo={{
          text: SalesChannelEnumTextMap.get(SalesChannelEnum.ElemeChannelId),
          channel: SalesChannelEnum.ElemeChannelId
        }}
        samName={IsUnityModelRss ? '批量发布' : null}
      />
    ),
    showBatchMeiTuanShanGou && (isHqStore || isSingleStore) && (
      <ExternalBatchRelease
        {...batchUpdateProps}
        onSuccess={onSuccess}
        validateSelectedItems={checkSelected}
        channelInfo={{
          text: SalesChannelEnumTextMap.get(SalesChannelEnum.MtShanGouChannelId),
          channel: SalesChannelEnum.MtShanGouChannelId
        }}
        samName={IsUnityModelRss ? '批量发布' : null}
      />
    )
  ].filter(Boolean);

  // 返回值各项对应 client/components/goods-batch-update/modules/index 中导出名称
  const batchUpdateModules = useMemo(() => {
    if (isSingleStore) {
      if (!HasSupplyChainAbility) {
        return ['goodsName', 'lifecycle', 'brand'];
      }
      return ['goodsName', 'lifecycle', 'brand', 'vendor'];
    }
    return ['goodsName', 'lifecycle', 'brand', 'vendor', 'deliveryType'];
  }, []);

  function getBatchModules(chan: GoodsBatchUpdateChan, hasFenxiao: boolean) {
    // 高级版走自有逻辑，见 https://doc.qima-inc.com/pages/viewpage.action?pageId=321519913 中 101 条
    // 新商品模型走自有逻辑
    if (IsUnityModelWithoutFwh) {
      let result = GoodsBatchUpdateChanAbilityModel.get(chan);
      /** 有一个是分销商品时就隐藏 */
      if (hasFenxiao) {
        result = result.filter(
          item => ['vipDiscount', 'lifecycle', 'sellTime', 'purchaseQuota'].includes(item) === false
        );
      }
      /** 所有选择的商品不支持改商品标签时隐藏 */
      if (chan === GoodsBatchUpdateChan.Online || chan === GoodsBatchUpdateChan.Offline) {
        const showGoodsLabelTab =
          getSupportGoodsLabelSelected(selected as ProductSearchDTO[]).length !== 0;
        if (!showGoodsLabelTab)
          result = result.filter(item => ['goodsLabel'].includes(item) === false);
      }
      return result;
    }
    // 其他版走原来逻辑
    return batchUpdateModules;
  }

  const showBatchOffline =
    (IsUnityModelRss || OfflineStoreGoodsManageAbilityValidOrExpired) && !hasFenxiao;

  /** 批量设置全部/网店/门店/外卖渠道 */
  const batchOpItemNodes = [
    ['全部', GoodsBatchUpdateChan.All, GoodsChannel.Storage] as const,
    ['网店', GoodsBatchUpdateChan.Online, GoodsChannel.Online] as const,
    showBatchOffline &&
      ([
        '门店',
        GoodsBatchUpdateChan.Offline,
        GoodsChannel.Offline,
        selectedSpuIdsWithoutLegacy,
        selectedWithoutLegacy,
        () => {
          const selectedLength = selected?.length;
          const selectedWithoutLegacyLength = selectedWithoutLegacy?.length;

          /** 历史商品不支持批量设置门店渠道 */
          if (selectedLength > 0 && !selectedWithoutLegacyLength) {
            Notify.warn('历史商品不支持批量设置-门店渠道');
            return false;
          }

          if (selectedLength > selectedWithoutLegacyLength) {
            Notify.warn(
              `已排除${selectedLength -
                selectedWithoutLegacyLength}个不支持批量设置-门店渠道的历史商品`
            );
          }

          return true;
        }
      ] as const),
    showBatchMeiTuan && (['美团外卖', GoodsBatchUpdateChan.MeiTuan, GoodsChannel.Meituan] as const),
    showBatchEleme && (['饿了么', GoodsBatchUpdateChan.Eleme, GoodsChannel.Eleme] as const),
    showBatchJd && (['京东外卖', GoodsBatchUpdateChan.Jd, GoodsChannel.Jd] as const),
    showBatchMeiTuanShanGou &&
      (['美团闪购', GoodsBatchUpdateChan.ShanGou, GoodsChannel.ShanGou] as const)
  ]
    .filter(Boolean)
    .map(([label, chan, menuType, selected, selectedItems, checkBeforeOpen]) => ({
      value: chan,
      label: (
        <BatchUpdate
          key={label}
          menuType={menuType}
          onClose={handleBatchUpdateComplete}
          modules={getBatchModules(chan, hasFenxiao)}
          titleLabel={`批量设置${label}渠道`}
          tips={
            <>
              <BatchUpdateHeader />
              {hasFenxiao ? (
                <Alert style={{ marginBottom: '16px' }}>分销商品不支持修改信息已隐藏</Alert>
              ) : null}
            </>
          }
          channel={chan}
          checkBeforeOpen={checkBeforeOpen}
          {...{
            ...batchUpdateProps,
            selected: selected || batchUpdateProps.selected,
            selectedItems: selectedItems || batchUpdateProps.selectedItems
          }}
          onConfirm={data => trackListBatchSetting(data)}
        >
          <div className={css.batch__popover__btn}>{label}渠道</div>
        </BatchUpdate>
      )
    }));

  const selectedItemsWithVipPrice = useMemo(
    () =>
      (selected as (ProductSearchDTO & ISpuSearchData)[]).filter(
        item =>
          isUnityModelData(item) &&
          item.sellChannelSettings.length > 0 &&
          // 非原材料
          item.inventoryType !== InventoryType.Material
      ) as ProductSearchDTO[],
    [selected]
  );
  // eslint-disable-next-line
  // @ts-ignore
  const storeMoreActionsList: IPublicCascaderItem[] = [
    {
      // 针对高级版的会员价立项在做，临时解决方案
      // eslint-disable-next-line
      // @ts-ignore
      // eslint-disable-next-line no-nested-ternary
      label: inNewMemberPrice ? (
        <LinkButton onClick={trackListBatchVipPrice} href="/v4/scrm/ump/member-price-rule">
          会员价
        </LinkButton>
      ) : UseNewVipPriceBtn ? (
        <VipPriceBatchNewButton data={goodsList} openBatchVipDrawer={openBatchVipDrawer}>
          会员价
        </VipPriceBatchNewButton>
      ) : (
        <div className="batch-vip-price-new-btn">
          <div className="batch-vip-price-new-btn-operate">
            <VipPriceBatchButton
              showChannel={IsUnityModelWithoutFwh}
              value={selectedItemsWithVipPrice.map((item: ProductSearchDTO) => item.itemId)}
              data={{
                shopSelector: KDT_ID,
                selectedRows: selectedItemsWithVipPrice
              }}
              onFetchData={onSuccess}
              onConfirm={trackListBatchVipPrice}
            >
              会员价
            </VipPriceBatchButton>
          </div>
          {ShowUpgradeNewVipPriceBtn && (
            <div className="batch-vip-price-new-btn-upgrade">
              <VipPriceUpgrade withGoodsAndVipPriceUpgrade={false} />
            </div>
          )}
        </div>
      ),
      value: '会员价',
      disabled: inNewMemberPrice
    },
    {
      // eslint-disable-next-line
      // @ts-ignore
      // eslint-disable-next-line prettier/prettier
      label: (
        <DeliveryTypeBtn
          onSuccess={() => onSuccess()}
          selected={selected as ProductSearchDTO[]}
          onConfirm={trackListBatchDelivery}
          disabledDeliveryTypeList={
            IsUnityModelRss && !global.BUSINESS.expressDelivery ? [DeliveryType.Express] : []
          }
          disabled={!checkAccess('修改配送方式')}
        />
      ),
      value: '配送方式',
      disabled: !checkAccess('修改配送方式')
    },
    {
      label: (
        <GoodsAttrBtn
          onSuccess={() => onSuccess()}
          selected={selected as ProductSearchDTO[]}
          onConfirm={trackListBatchGoodsAttr}
        />
      ),
      value: '商品属性',
      disabled: !checkAccess('修改商品属性')
    },
    {
      label: '批量设置',
      value: '批量设置',
      // eslint-disable-next-line
      // @ts-ignore
      children: batchOpItemNodes
    },
    {
      label: (
        <DeleteAction
          spuIds={selectedSpuIdsWithoutLegacy}
          itemIds={selectedOnlineItemIdsWithoutLegacy}
          legacyIds={selectedLegacyItemIds}
          delRelated={selectedDelRelated}
          onSuccess={onSuccess}
          onConfirm={trackListBatchDelete}
        >
          删除
        </DeleteAction>
      ),
      value: '删除',
      disabled: !checkAccess('删除')
    }
  ].filter(Boolean);

  if (isFrontWarehouse) {
    return null;
  }

  const keysGroup = map(datasets, (item: ProductSearchDTO | ISpuSearchData) =>
    isUnityModelData(item) ? item.idSummary.spuId : item.spuId
  );
  const handleSelect = (spuIds: number[]) =>
    setSelected(
      (datasets as any[]).filter(good =>
        spuIds.includes(isUnityModelData(good) ? good.idSummary.spuId : good.spuId)
      )
    );

  const actions: JSX.Element[] = [
    // 分类
    <BatchClassification
      list={catList}
      selected={selected}
      selectedSpuIds={selectedSpuIds}
      selectedSpuIdsWithoutLegacy={selectedSpuIdsWithoutLegacy}
      onSuccess={onSuccess}
    />,
    // 批量发布
    !HasMultiChannelProductManageAbility && !isBranchStore && releaseItems.length > 0 && (
      <PopSelect
        popoverClass="batch-release-popover"
        className={classnames('batch-release', {
          [css.batch__label]: IsUnityModelWithoutFwh
        })}
        label="批量发布到"
        items={releaseItems}
      />
    ),
    /** 多渠道-批量发布 */
    HasMultiChannelProductManageAbility && (
      <MultiChannelBatchPublish {...batchUpdateProps} validateSelectedItems={checkSelected} />
    ),
    /** 多渠道-改店铺可售 */
    HasMultiChannelProductManageAbility && !isBranchStore && (
      <PopSelect
        popoverClass="batch-release-popover"
        className={classnames('batch-release', {
          [css.batch__label]: IsUnityModelWithoutFwh
        })}
        label="改店铺可售"
        items={[
          <BatchSale
            selectedItems={selected}
            sales={SubShopStatus.Sale}
            onSuccess={onSuccess}
            onConfirm={() => trackListBatchSale({ sale: true })}
          />,
          <BatchSale
            selectedItems={selected}
            sales={SubShopStatus.NoSale}
            onSuccess={onSuccess}
            onConfirm={() => trackListBatchSale({ noSale: true })}
          />
        ]}
      />
    ),
    // 批量上下架, NOTE: 针对大规模连锁做白名单隐藏很不合理,PM要求这么做@新风
    IsUnityModelWithoutFwh &&
      !disableBatchRelease &&
      !HasMultiChannelProductManageAbility &&
      !isBranchStore && (
        <PopSelect
          popoverClass="batch-release-popover"
          className={classnames('batch-release-shelf', {
            [css.batch__label]: IsUnityModelWithoutFwh
          })}
          label="渠道上/下架"
          items={IsUnityModelRss ? rssRackOptions : rackOptions}
        />
      ),
    /** 多渠道-批量上下架 */
    HasMultiChannelProductManageAbility && !isBranchStore && (
      <PopSelect
        popoverClass="batch-release-popover"
        className={classnames('batch-release-shelf', {
          [css.batch__label]: IsUnityModelWithoutFwh
        })}
        label="渠道上/下架"
        items={[
          <BatchShelf
            selectedItems={selected}
            display={ProductDisplayStatus.OnShelves}
            onSuccess={onSuccess}
          >
            <div className={css.batch__popover__btn}>上架</div>
          </BatchShelf>,
          <BatchShelf
            selectedItems={selected}
            display={ProductDisplayStatus.OffShelves}
            onSuccess={onSuccess}
          >
            <div className={css.batch__popover__btn}>下架</div>
          </BatchShelf>,
          <BatchShelf
            selectedItems={selected}
            display={ProductDisplayStatus.NoSell}
            onSuccess={onSuccess}
          >
            <div className={css.batch__popover__btn}>不可售</div>
          </BatchShelf>
        ]}
      />
    ),
    // 删除
    (!IsUnityModelWithoutFwh || isBranchStore) && (
      <DeleteAction
        spuIds={selectedSpuIdsWithoutLegacy}
        itemIds={selectedOnlineItemIdsWithoutLegacy}
        legacyIds={selectedLegacyItemIds}
        delRelated={selectedDelRelated}
        onSuccess={onSuccess}
        onConfirm={trackListBatchDelete}
      />
    ),
    // 改价格
    IsUnityModelWithoutFwh && !isBranchStore && (
      <ModifyPrices
        selectedItems={selected as ProductSearchDTO[]}
        onSuccess={onSuccess}
        onConfirm={trackListBatchPrice}
      />
    ),

    // 改总部库存
    IsUnityModelUnifiedShop && !isBranchStore && (
      <ModifyStock
        selectedItems={selected as ProductSearchDTO[]}
        onSuccess={onSuccess}
        onConfirm={trackListBatchStock}
      />
    ),

    // 零售单店，改库存
    IsUnityModelRss && (
      <RssModifyStock selectedItems={selected as ProductSearchDTO[]} onSuccess={onSuccess} />
    ),

    // 非高级版的“批量设置”
    !IsUnityModelWithoutFwh && !isBranchStore && (
      <BatchUpdate
        menuType={GoodsChannel.Storage}
        onClose={handleBatchUpdateComplete}
        modules={batchUpdateModules}
        {...batchUpdateProps}
      />
    ),
    // 更多
    IsUnityModelWithoutFwh && !isBranchStore && (
      <StorageMoreActions options={storeMoreActionsList} />
    ),
    // 一键重建异常商品
    HasLegacyAbility && !isBranchStore ? (
      <BatchRebuild
        onClick={() => {
          onRebuild(selectedLegacyPendingItemIds);
        }}
      />
    ) : null
  ].filter(Boolean);

  return (
    <>
      <TableBatchCpn
        keys={keysGroup}
        onSelect={handleSelect}
        items={actions}
        selected={(selected as { itemId: number }[]).map(item => item.itemId)}
      />
      {/* 因为批量会员价按钮会销毁，不能在销毁的组件内控制抽屉展示，只能写在外层 */}
      <ShowCheckMsgDialog
        validateData={validateData}
        goodsIds={goodsList}
        dialogVisible={visible}
        onClose={() => setVisible(false)}
        onConfirm={() => {
          setVisible(false);
        }}
        onSubmitCallback={() => {
          onSuccess();
          trackListBatchVipPrice();
        }}
      />
    </>
  );
};

export default Batch;
