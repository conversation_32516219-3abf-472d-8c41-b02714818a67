import * as React from 'react';
import { useState, useEffect, useMemo } from 'react';
import { Grid, Notify } from 'zent';
import { IFetchTableTransProps } from '@youzan/retail-components';
import { isBranchStore, isFrontWarehouse } from '@youzan/utils-shop';
import { IClassification, ISpuSearchData } from '@youzan/zan-hasaki';
import { isNil, map } from 'lodash';

import { updateName } from 'route/api';

import { IsUnityModelUnifiedShop, IsUnityModelRss, IsUnityModelWithoutFwh } from 'common/constants';
import {
  BatchModifySelectEnum,
  ListBatchContext,
  useBatchTableSelect
} from 'cpn/table-batch-way-select';
import { isHqStoreAndNewModel } from 'common/constants/data-permissions';
import TableBatch from './batch';
import { ProductSearchDTO } from './dto';
import { isUnityModelData } from './helper';
import getUnityModelRSSColumns from './rss-columns';
import getLiteColumns from './lite-columns';
import getUniColumns from './uni-columns';
import getBranchColumns from './branch-columns';
import { BillStatus, BillStatusTextMap } from './constants';

interface IGoodsTableProps extends IFetchTableTransProps {
  datasets: ProductSearchDTO[] | ISpuSearchData[];
  id2cat: {
    [key: number]: string;
  };
  catList: IClassification[];
  onRebuild(fetch: IFetchTableTransProps['fetch'], itemIds: number[]): void;
}

const tableRowIdField = IsUnityModelWithoutFwh
  ? 'itemId' // idSummary.spuId 不一定存在（非实物商品），故取 itemId
  : 'spuId';

const getColumns = (() => {
  if (isBranchStore) {
    return getBranchColumns;
  }
  if (IsUnityModelUnifiedShop) {
    return getLiteColumns;
  }
  if (IsUnityModelRss) {
    return getUnityModelRSSColumns;
  }
  // 不是新商品模型
  return getUniColumns;
})();

const GoodsTable: React.FC<IGoodsTableProps> = ({
  datasets,
  id2cat,
  catList,
  onRebuild,
  ...tableProps
}) => {
  const formattedDatasets = useMemo(
    () =>
      map(datasets, (data: ProductSearchDTO | ISpuSearchData) =>
        isUnityModelData(data)
          ? ({
              get spuId() {
                return (data as ProductSearchDTO).idSummary.spuId;
              },
              ...data
            } as ProductSearchDTO)
          : data
      ) as ProductSearchDTO[] | ISpuSearchData[],
    [datasets]
  );
  const [selected, setSelected] = useState<ProductSearchDTO[] | ISpuSearchData[]>([]);
  const {
    currentModify,
    setCurrentModify,
    inverseSelectData,
    setInverseSelectData
  } = useBatchTableSelect();

  const dataFilterDisabled = useMemo(() => {
    return (formattedDatasets as ProductSearchDTO[]).filter(
      (item: ProductSearchDTO) => !item.hasPeriodStockNum
    );
  }, [formattedDatasets]);
  const selection = !isFrontWarehouse && {
    selectedRowKeys: map(
      selected,
      (good: ProductSearchDTO | ISpuSearchData) => good[tableRowIdField as keyof typeof good]
    ),
    getSelectionProps(data: ProductSearchDTO) {
      if (isBranchStore) {
        const { selfCreate: { orderApprovalStatus, itemOperateType } = {} } = data;
        if (isNil(orderApprovalStatus)) {
          return {
            disabled: true,
            reason: '总部创建商品不支持操作'
          };
        }
        if (
          !(
            [BillStatus.Resolved, BillStatus.Delete].includes(orderApprovalStatus) ||
            (itemOperateType === 2 && orderApprovalStatus === BillStatus.Rejected)
          )
        ) {
          return {
            disabled: true,
            reason: `当前商品${BillStatusTextMap.get(orderApprovalStatus)}，暂不支持编辑`
          };
        }
      }
      // 禁用掉有时段库存的商品
      const { hasPeriodStockNum } = data;
      return {
        disabled: hasPeriodStockNum,
        reason: '活动预订商品不支持操作'
      };
    },
    onSelect(_selected: number[], selectedGoods: ProductSearchDTO[]) {
      if (isHqStoreAndNewModel) {
        // 获取当前页除了禁用的itemIds
        const curPageItemIds = dataFilterDisabled.map(item => item.itemId);
        if (currentModify === BatchModifySelectEnum.MODIFY_ALL_GOODS) {
          // 获取当前页未选中的itemIds
          const inverseItemIdData = dataFilterDisabled
            .filter(item => !_selected.includes(item.itemId))
            .map(item => item.itemId);
          // 将其他页未选中的数据和当前页未选中的数据合并
          setInverseSelectData([
            ...inverseSelectData.current.filter(itemId => !curPageItemIds.includes(itemId)),
            ...inverseItemIdData
          ]);
        } else {
          setInverseSelectData(_selected);
        }

        setSelected([
          ...(selected || [] as any[]).filter(
            (item: ProductSearchDTO) => !curPageItemIds.includes(item.itemId)
          ),
          ...selectedGoods
        ] as any);
      } else {
        setSelected(selectedGoods);
      }
    }
  };

  const { onChange, pageInfo, fetch } = tableProps;

  const onSuccess = () => {
    setSelected([]);
    onChange(pageInfo);
  };

  // 更新名称
  const onUpdateName = ({ spuId, name }: { spuId: number; name: string }) => {
    updateName({
      spuId,
      name
    })
      .then(() => {
        onChange(pageInfo);
        Notify.success('更新成功');
      })
      .catch(err => {
        Notify.error(err.msg || '更新名称接口报错');
      });
  };

  useEffect(() => {
    if (!isHqStoreAndNewModel) {
      // 清空已选数据
      setSelected([]);
    }
  }, [formattedDatasets]);

  return (
    <ListBatchContext.Provider
      value={{
        total: tableProps.pageInfo.total,
        onModifyWayChange: (key: BatchModifySelectEnum) => {
          setCurrentModify(key);
          setSelected([]);
        }
      }}
    >
      <Grid
        className="storage-table has-selection"
        rowKey={tableRowIdField}
        datasets={formattedDatasets as Readonly<any[]>}
        columns={getColumns({
          onUpdateName,
          id2cat,
          onSuccess,
          fetch,
          onRebuild: onRebuild.bind(null, fetch)
        })}
        selection={selection as any}
        scroll={{ x: 1400 }}
        stickyBatch
        {...tableProps}
        batchRender={() => (
          <TableBatch
            datasets={formattedDatasets}
            onSuccess={onSuccess}
            onRebuild={onRebuild.bind(null, fetch)}
            catList={catList}
            selected={selected}
            setSelected={setSelected}
          />
        )}
      />
    </ListBatchContext.Provider>
  );
};

export default GoodsTable;
