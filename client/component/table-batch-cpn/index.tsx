import * as React from 'react';
import cx from 'classnames';
import { Notify } from 'zent';
import { GoodsType, InventoryType } from '@youzan/zan-hasaki';
import { Divider } from '@youzan/react-components';
import { EnumMarkCode } from '@youzan/goods-domain-definitions';

import { ProductSearchDTO } from 'route/storage/list';
import css from './index.scss';
import TableBatchWaySelect from 'cpn/table-batch-way-select';
import { isHqStoreAndNewModel } from 'common/constants/data-permissions';

interface IProps {
  items: React.ReactNodeArray;
  keys: number[];
  selected: number[] | string[];
  onSelect: (selected: number[] | string[]) => void;
  className?: string;
}

const TableBatchCpn: React.FC<IProps> = ({ items, keys, selected, onSelect, className }) => {
  const selectedCount = selected.length;
  const hasSelected = selectedCount > 0;
  const hasSelectedAll = selectedCount === keys.length;

  const select = () => {
    onSelect(hasSelectedAll ? [] : keys);
  };

  const selectTextItems = [
    <span onClick={select} className="batch__pick">
      当页全选
    </span>
  ];

  if (hasSelected) {
    selectTextItems.push(<span className="batch__count">{`已选 ${selectedCount} 项`}</span>);
  }

  return (
    <div className={cx(css.batch, className)}>
      {isHqStoreAndNewModel ? <TableBatchWaySelect /> : <Divider items={selectTextItems} />}
      <div className="batch__list">
        {items &&
          items.map((item, idx) => (
            <div key={idx} className="batch__item">
              {item}
            </div>
          ))}
      </div>
    </div>
  );
};

export default TableBatchCpn;

/** 单次最大设置商品数量 */
export const MaxSelectedCount = 50;

export function checkSelected(selected: any[], warn = true) {
  const selectedCount = selected?.length || 0;
  if (selectedCount <= 0) {
    warn && Notify.warn('请选择商品');
    return false;
  }
  if (selectedCount > MaxSelectedCount) {
    warn && Notify.warn(`单次批量操作商品数量不能超过 ${MaxSelectedCount} 个`);
    return false;
  }
  return true;
}

/** 虚拟商品 */
const VirtualGoods = 2;
/** 电子卡劵 */
const CardGoods = 3;

export enum CheckFlag {
  /** 虚拟商品 or 电子卡券 or 付费优惠券 */
  VirtualGoodsAndCardGoods = 1 << 0,
  /** 分销 */
  Fenxiao = 1 << 1,
  /** 原材料 */
  Material = 1 << 2,
  /** 历史商品 */
  Legacy = 1 << 4,
  /** 全部 */
  All = CheckFlag.Fenxiao |
    CheckFlag.Material |
    CheckFlag.VirtualGoodsAndCardGoods |
    CheckFlag.Legacy
}

const CheckFlagConditionMap = {
  [CheckFlag.VirtualGoodsAndCardGoods]: (goods: ProductSearchDTO) =>
    goods.isVirtual === VirtualGoods ||
    goods.isVirtual === CardGoods ||
    goods.markCode === EnumMarkCode.Coupon,
  [CheckFlag.Fenxiao]: (goods: ProductSearchDTO) => goods.goodsType === GoodsType.FenXiao,
  [CheckFlag.Material]: (goods: ProductSearchDTO) => goods.inventoryType === InventoryType.Material,
  [CheckFlag.Legacy]: (goods: ProductSearchDTO) =>
    goods.prodItemMigrationTag &&
    goods.prodItemMigrationTag !== 3 &&
    goods.prodItemMigrationTag !== 4 &&
    goods.prodItemMigrationTag !== 5
};

const CheckFlagTextMap = {
  [CheckFlag.VirtualGoodsAndCardGoods]: '虚拟商品、电子卡券、付费优惠券',
  [CheckFlag.Fenxiao]: '分销商品',
  [CheckFlag.Material]: '原材料商品',
  [CheckFlag.Legacy]: '历史商品'
};

const getBitListFrom = <T extends number>(bit: T) => {
  let i = 1 << 0,
    j = bit;
  const bitList = [];
  while (j) {
    const result = (j ^ i) as T;
    if (result < j) {
      bitList.push(i);
      j = result;
    }
    i <<= 1;
  }
  return bitList;
};

enum FilterKey {
  Some = 'some',
  Every = 'every'
}

const filterGoods = (
  goodsList: ProductSearchDTO[],
  checkFlagList: CheckFlag[],
  filterKey = FilterKey.Every,
  exclude = false
) => {
  return goodsList.filter(item =>
    checkFlagList
      .map(flag => {
        const result = CheckFlagConditionMap[flag](item);
        return exclude ? !result : result;
      })
      [filterKey](checkResult => checkResult)
  );
};
export function checkGoodsCanRelease(
  goodsList: ProductSearchDTO[],
  channelName: string,
  checkFlag: CheckFlag
) {
  const checkFlagList = getBitListFrom(checkFlag);
  const filterListByCheckFlag = filterGoods(goodsList, checkFlagList, FilterKey.Some);
  let notifyMsg = '';

  if (filterListByCheckFlag.length > 0 && filterListByCheckFlag.length === goodsList.length) {
    notifyMsg = `不能发布到${channelName}`;
    notifyMsg = checkFlagList.reduce(
      (acc, checkFlag, i) => `${CheckFlagTextMap[checkFlag]}${i === 0 ? '' : '、'}${acc}`,
      notifyMsg
    );
  }
  if (notifyMsg) {
    Notify.warn(notifyMsg);
    return false;
  }
  return true;
}

export function filterGoodsCanRelease(goodsList: ProductSearchDTO[], checkFlag: CheckFlag) {
  return filterGoods(goodsList, getBitListFrom(checkFlag), FilterKey.Every, true).map(
    goods => goods.idSummary?.spuId
  );
}
