import React from 'react';
import { Radio } from 'zent';

// 库存扣减方式枚举
const StockDeductionMode = {
  ORDER: 1, // 拍下减库存
  PAYMENT: 2 // 付款减库存
};

interface IProps {
  input: {
    value: number;
    onChange: (val: number) => void;
  };
}

const Component = (props: IProps) => {
  const { onChange, value } = props.input;

  const handleChange = (e: any) => {
    onChange(Number(e.target.value));
  };

  return (
    <Radio.Group value={value} onChange={handleChange}>
      <Radio value={StockDeductionMode.ORDER}>拍下减库存</Radio>
      <Radio value={StockDeductionMode.PAYMENT}>付款减库存</Radio>
    </Radio.Group>
  );
};

export default Component;
