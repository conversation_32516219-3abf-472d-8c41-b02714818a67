import React from 'react';
import { Field } from '@youzan/retail-form';

import Component from './cpn';
import './index.scss';

interface IProps {
  name: string;
}

const StockDeductionModeBatch = (props: IProps) => {
  return (
    <div className='stock-deduction-mode-batch'>
      <Field
        component={Component}
        label="库存扣减方式："
        {...props}
      />
    </div>
  );
};

export default StockDeductionModeBatch;
