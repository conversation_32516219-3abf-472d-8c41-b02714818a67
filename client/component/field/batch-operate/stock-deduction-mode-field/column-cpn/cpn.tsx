import React from 'react';
import { Select } from '@youzan/biz-select-center';

// 库存扣减方式选项
const STOCK_DEDUCTION_OPTIONS = [
  {
    text: '拍下减库存',
    value: 1
  },
  {
    text: '付款减库存',
    value: 2
  }
];

interface IProps {
  input: {
    value: number;
    onChange: (val: number) => void;
  };
}

const Component = (props: IProps) => {
  const { onChange, value } = props.input;

  const handleChange = (val: number) => {
    onChange(val);
  };

  return (
    <Select
      width={120}
      data={STOCK_DEDUCTION_OPTIONS}
      value={value}
      onChange={handleChange}
      placeholder="请选择"
    />
  );
};

export default Component;
