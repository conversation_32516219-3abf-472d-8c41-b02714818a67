import React from 'react';
import { Field } from '@youzan/retail-form';

import Component from './cpn';
import './index.scss';

interface IProps {
  name: string;
}

const OfflineGoodsSpecialLabelBatch = (props: IProps) => {
  return (
    <div className='offline-goods-special-label-batch'>
      <Field
        component={Component}
        label="商品关键标签："
        {...props}
      />
    </div>
  );
};

export default OfflineGoodsSpecialLabelBatch;
