import React from 'react';
import { Field } from '@youzan/retail-form';

import Component from './cpn';
import './index.scss';

interface IProps {
  name: string;
}

const ShareDescBatch = (props: IProps) => {
  return (
    <div className='share-desc-batch'>
      <Field
        component={Component}
        label="分享描述："
        {...props}
      />
    </div>
  );
};

export default ShareDescBatch;
