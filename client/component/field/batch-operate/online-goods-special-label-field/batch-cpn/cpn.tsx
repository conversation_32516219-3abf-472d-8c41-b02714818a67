import React from 'react';
import { FeatureLabelSelector } from 'cpn/feature-label-selector';
import { EFeatureLabelType } from 'cpn/feature-label-selector/constants';

interface IProps {
  input: {
    value: any;
    onChange: (val: any) => void;
  };
}

const Component = (props: IProps) => {
  const { onChange, value } = props.input;

  const handleChange = (val: any) => {
    onChange(val);
  };

  return (
    <FeatureLabelSelector
      type={EFeatureLabelType.Special}
      value={value}
      onChange={handleChange}
      disabledSelect={false}
    />
  );
};

export default Component;
