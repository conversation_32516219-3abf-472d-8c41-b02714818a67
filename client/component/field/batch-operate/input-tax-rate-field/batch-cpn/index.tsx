import React from 'react';
import { Field } from '@youzan/retail-form';

import Component from './cpn';
import style from './index.scss';

interface IProps {
  name: string;
}

const InputTaxRateBatch = (props: IProps) => {
  return (
    <div className={style.inputTaxRate}>
      <Field
        component={Component}
        label="进项税率："
        {...props}
        required
        helpDesc="设置商品的进项税率，用于税务计算"
      />
    </div>
  );
};

export default InputTaxRateBatch;
