import React, { useState, useEffect } from 'react';
import { Notify } from 'zent';
import { Select } from '@youzan/biz-select-center';
import { request } from '@youzan/retail-utils';

interface Tax {
  text: string;
  value: string;
}

function getAllTax(): Promise<Tax[]> {
  return request<Tax[]>({
    url: '/youzan.retail.product.getalltax/1.0.0'
  });
}

interface IProps {
  input: {
    value: any;
    onChange: (val: any) => void;
  };
}

const Component = (props: IProps) => {
  const { value, onChange } = props.input;
  const [tax, setTax] = useState<Tax[]>([]);

  useEffect(() => {
    getAllTax()
      .then(taxData => {
        setTax(taxData);
      })
      .catch(err => {
        Notify.error(err.msg || '获取税率失败');
      });
  }, []);

  const handleChange = (val: string) => {
    onChange(val);
  };

  return <Select width={120} data={tax} value={value} onChange={handleChange} placeholder="请选择" />;
};

export default Component;
