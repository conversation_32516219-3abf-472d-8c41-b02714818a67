import React from 'react';
import { Field } from '@youzan/retail-form';

import Component from './cpn';
import './index.scss';

interface IProps {
  name: string;
}

const ShelfLifeWarningBatch = (props: IProps) => {
  return (
    <div className='shelf-life-warning'>
      <Field
        component={Component}
        label="保质期管理："
        {...props}
        helpDesc="开启后可设置商品保质期天数和预警天数"
      />
    </div>
  );
};

export default ShelfLifeWarningBatch;
