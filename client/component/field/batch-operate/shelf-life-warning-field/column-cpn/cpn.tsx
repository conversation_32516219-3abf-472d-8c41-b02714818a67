import React, { useCallback } from 'react';
import { Radio, NumberInput } from 'zent';
import { SwitchStatusEnum } from '@youzan/zan-hasaki';
import './index.scss';

interface IProps {
  input: {
    value: {
      isShelfLifeWarning?: SwitchStatusEnum;
      shelfLifeDays?: number;
      warningDays?: number;
    };
    onChange: (val: any) => void;
  };
}

const Component = (props: IProps) => {
  const { onChange, value = {} } = props.input;
  const { isShelfLifeWarning, shelfLifeDays, warningDays } = value;

  const onOptionChange = useCallback(
    e => {
      const { value: optionValue } = e.target;
      const newValue = {
        ...value,
        isShelfLifeWarning: optionValue
      };
      
      if (optionValue === SwitchStatusEnum.Off) {
        newValue.shelfLifeDays = null;
        newValue.warningDays = null;
      }
      
      onChange(newValue);
    },
    [value, onChange]
  );

  const onFieldChange = useCallback(
    (field: string) => (fieldValue: number) => {
      onChange({
        ...value,
        [field]: fieldValue
      });
    },
    [value, onChange]
  );

  return (
    <div className='shelf-life-warning-cln-container'>
      <Radio.Group value={isShelfLifeWarning} onChange={onOptionChange}>
        <Radio value={SwitchStatusEnum.On}>开启</Radio>
        <Radio value={SwitchStatusEnum.Off}>关闭</Radio>
      </Radio.Group>
      {isShelfLifeWarning === SwitchStatusEnum.On && (
        <div className='daysWrapper'>
          <div className='fieldGroup'>
            <span>保质期天数：</span>
            <NumberInput
              integer
              value={shelfLifeDays}
              onChange={onFieldChange('shelfLifeDays')}
              width={60}
              min={1}
              max={99999}
              placeholder="请输入"
            />
          </div>
          <div className='fieldGroup'>
            <span>预警天数：</span>
            <NumberInput
              integer
              value={warningDays}
              onChange={onFieldChange('warningDays')}
              width={60}
              min={0}
              max={99998}
              placeholder="请输入"
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default Component;
