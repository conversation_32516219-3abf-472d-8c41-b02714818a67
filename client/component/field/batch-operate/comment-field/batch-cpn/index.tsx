import React, { useState } from 'react';
import { Button } from 'zent';

import Component from './cpn';
import './index.scss';

interface IProps {
  name: string;
  onConfirm?: (value: any) => void;
}

const CommentBatch = (props: IProps) => {
  const [tempValue, setTempValue] = useState([]);

  const handleConfirm = () => {
    if (props.onConfirm) {
      props.onConfirm(tempValue);
    }
  };

  return (
    <div className='comment-batch'>
      <Component
          input={{
            value: tempValue,
            onChange: setTempValue
          }}
      />
      <div>
        <Button type="primary" onClick={handleConfirm}>
          确定
        </Button>
      </div>
    </div>
  );
};

export default CommentBatch;
