import React, { useState } from 'react';
import { Field } from '@youzan/retail-form';
import { Button, Dialog } from 'zent';

import Component from './cpn';
import './index.scss';

interface IProps {
  name: string;
  onConfirm?: (value: any) => void;
}

const CommentBatch = (props: IProps) => {
  const [visible, setVisible] = useState(false);
  const [tempValue, setTempValue] = useState([]);

  const handleOpenDialog = () => {
    setVisible(true);
  };

  const handleCloseDialog = () => {
    setVisible(false);
  };

  const handleConfirm = () => {
    if (props.onConfirm) {
      props.onConfirm(tempValue);
    }
    setVisible(false);
  };

  return (
    <div className='comment-batch'>
      <Button type="primary" onClick={handleOpenDialog}>
        商品留言
      </Button>

      <Dialog
        visible={visible}
        title="商品留言"
        onClose={handleCloseDialog}
        style={{ width: '600px' }}
        footer={
          <div>
            <Button onClick={handleCloseDialog}>取消</Button>
            <Button type="primary" onClick={handleConfirm}>
              确定
            </Button>
          </div>
        }
      >
        <Component
          input={{
            value: tempValue,
            onChange: setTempValue
          }}
        />
      </Dialog>
    </div>
  );
};

export default CommentBatch;
