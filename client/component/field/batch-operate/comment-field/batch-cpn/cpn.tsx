import React from 'react';
import { Select, Input, Checkbox } from 'zent';
import { CommentType, CommentData } from '../types';
import { Link as SamLink } from '@youzan/sam-components';

const CommentTypes = [
  { text: '文本', key: CommentType.Text },
  { text: '数字', key: CommentType.Number },
  { text: '邮箱', key: CommentType.Email },
  { text: '日期', key: CommentType.Date },
  { text: '身份证', key: CommentType.IdCard },
  { text: '图片', key: CommentType.Image },
  { text: '手机号', key: CommentType.Mobile },
  { text: '时间', key: CommentType.Time }
];

export const MaxTitleLength = 10;
export const MaxCommentsLength = 10;

interface IProps {
  input: {
    value: CommentData[];
    onChange: (val: CommentData[]) => void;
  };
}

interface CommentItemProps {
  data: CommentData;
  onChange: (data: CommentData) => void;
  onDelete: () => void;
}

const CommentItem: React.FC<CommentItemProps> = ({ data, onChange, onDelete }) => {
  const handleTypeChange = (value: any) => {
    const newType = value ? value.key : CommentType.Text;
    onChange({
      ...data,
      type: newType,
      // 重置相关选项
      multiple: newType === CommentType.Text ? data.multiple : false,
      includeDate: newType === CommentType.Time ? data.includeDate : false
    });
  };

  const handleTitleChange = (e: any) => {
    onChange({
      ...data,
      title: e.target.value
    });
  };

  const handleRequiredChange = (e: any) => {
    onChange({
      ...data,
      required: e.target.checked
    });
  };

  const handleMultipleChange = (e: any) => {
    onChange({
      ...data,
      multiple: e.target.checked
    });
  };

  const handleIncludeDateChange = (e: any) => {
    onChange({
      ...data,
      includeDate: e.target.checked
    });
  };

  const selectedType = CommentTypes.find(type => type.key === data.type) || CommentTypes[0];
  const multipleVisible = data.type === CommentType.Text;
  const timeVisible = data.type === CommentType.Time;

  return (
    <div style={{ marginBottom: '16px', padding: '12px', border: '1px solid #e8e8e8', borderRadius: '4px' }}>
      <div style={{ display: 'flex', alignItems: 'center', flexWrap: 'wrap', gap: '8px' }}>
        <Select
          options={CommentTypes}
          value={selectedType}
          onChange={handleTypeChange}
          width={100}
        />
        <span>留言标题为</span>
        <Input
          value={data.title}
          onChange={handleTitleChange}
          style={{ width: '120px' }}
          maxLength={MaxTitleLength}
        />
        <Checkbox
          checked={data.required}
          onChange={handleRequiredChange}
        >
          必填
        </Checkbox>
        {multipleVisible && (
          <Checkbox
            checked={data.multiple}
            onChange={handleMultipleChange}
          >
            多行
          </Checkbox>
        )}
        {timeVisible && (
          <Checkbox
            checked={data.includeDate}
            onChange={handleIncludeDateChange}
          >
            含日期
          </Checkbox>
        )}
        <SamLink onClick={onDelete}>删除</SamLink>
      </div>
    </div>
  );
};

const Component = (props: IProps) => {
  const { onChange, value = [] } = props.input;

  const handleItemChange = (index: number, newData: CommentData) => {
    const newValue = [...value];
    newValue[index] = newData;
    onChange(newValue);
  };

  const handleDelete = (index: number) => {
    const newValue = value.filter((_, i) => i !== index);
    onChange(newValue);
  };

  const handleAdd = () => {
    if (value.length < MaxCommentsLength) {
      const newValue = [...value, {
        title: `留言 ${value.length + 1}`,
        type: CommentType.Text,
        multiple: false,
        required: false,
        includeDate: false
      }];
      onChange(newValue);
    }
  };

  return (
    <div>
      {value.map((item, index) => (
        <CommentItem
          key={index}
          data={item}
          onChange={(newData) => handleItemChange(index, newData)}
          onDelete={() => handleDelete(index)}
        />
      ))}
      {value.length < MaxCommentsLength && (
        <SamLink onClick={handleAdd}>添加字段</SamLink>
      )}
      <div style={{ marginTop: '16px', color: '#666', fontSize: '12px' }}>
        购买商品时让买家输入留言，最多可设置 {MaxCommentsLength} 条留言。
      </div>
    </div>
  );
};

export default Component;
