import React, { useCallback, useState, useEffect } from 'react';
import {
  Form,
  FormControl,
  FieldSet,
  FieldUtils,
  Select,
  Input,
  Checkbox,
  ISelectProps,
  IInputCoreProps,
  ICheckboxProps,
  IInputChangeEvent,
  ICheckboxEvent,
  FormDescription
} from 'zent';

import { CommentType, CommentData } from '../types';

const { useFieldArray, useField, useFieldValue } = Form;

const CommentTypes = [
  { text: '文本', key: CommentType.Text },
  { text: '数字', key: CommentType.Number },
  { text: '邮箱', key: CommentType.Email },
  { text: '日期', key: CommentType.Date },
  { text: '身份证', key: CommentType.IdCard },
  { text: '图片', key: CommentType.Image },
  { text: '手机号', key: CommentType.Mobile },
  { text: '时间', key: CommentType.Time }
];

export const MaxTitleLength = 10;
export const MaxCommentsLength = 10;

interface IProps {
  input: {
    value: CommentData[];
    onChange: (val: CommentData[]) => void;
  };
}

const SelectField: React.FC<ISelectProps<CommentType, typeof CommentTypes[number]>> = function (
  props
) {
  const model = Form.useField<CommentType | null>('type', null);
  const handleSelectChange = FieldUtils.useMulti(
    useCallback(() => {
      model.isTouched = true;
    }, [model]),
    FieldUtils.makeChangeHandler(model, Form.ValidateOption.Default)
  );
  const [selected, setSelected] = useState<typeof CommentTypes[number] | null>(null);
  useEffect(() => {
    const [selected] = CommentTypes.filter((type) => type.key === model.value);
    setSelected(selected ?? null);
  }, [model.value]);
  return (
    <Select
      {...props}
      width={180}
      multiple={false}
      inline
      value={selected}
      onChange={(value) => handleSelectChange(value ? value.key : null)}
    />
  );
};

const InputField: React.FC<IInputCoreProps> = function (props) {
  const model = useField('title', '');
  const handleInputChange = FieldUtils.useMulti(
    useCallback(() => {
      model.isTouched = true;
    }, [model]),
    FieldUtils.usePipe(
      (ev: IInputChangeEvent) => ev.target.value,
      FieldUtils.makeChangeHandler(model, Form.ValidateOption.Default)
    )
  );
  return <Input {...props} value={model.value} onChange={handleInputChange} />;
};

const CheckboxField: React.FC<ICheckboxProps<boolean> & { field: string }> = function ({
  children,
  field,
  ...props
}) {
  const model = useField(field, false);
  const handleChecked = FieldUtils.useMulti(
    useCallback(() => {
      model.isTouched = true;
    }, [model]),
    FieldUtils.usePipe(
      (ev: ICheckboxEvent<boolean>) => ev.target.checked,
      FieldUtils.makeChangeHandler(model, Form.ValidateOption.Default)
    )
  );
  return (
    <Checkbox {...props} checked={model.value} onChange={handleChecked}>
      {children}
    </Checkbox>
  );
};

function useHandleFieldModel(
  multipleVisible: boolean,
  timeVisible: boolean,
  typeValue: CommentType | null
) {
  const multipleModel = useField('multiple', false);
  const timeModel = useField('includeDate', false);
  const handleMultipleFieldChange = FieldUtils.useMulti(
    useCallback(() => {
      multipleModel.isTouched = true;
    }, [multipleModel]),
    FieldUtils.makeChangeHandler(multipleModel, Form.ValidateOption.IncludeUntouched)
  );
  const handleTimeFieldChange = FieldUtils.useMulti(
    useCallback(() => {
      timeModel.isTouched = true;
    }, [timeModel]),
    FieldUtils.makeChangeHandler(timeModel, Form.ValidateOption.IncludeUntouched)
  );
  useEffect(() => {
    if (!multipleVisible) {
      handleMultipleFieldChange(false);
    }
    if (!timeVisible) {
      handleTimeFieldChange(false);
    }
  }, [handleMultipleFieldChange, handleTimeFieldChange, multipleVisible, timeVisible, typeValue]);
}

const CommentItem: React.FC<{
  inputProps?: IInputCoreProps;
  onDelete: () => void;
}> = function CommentItem({ inputProps = {}, onDelete }) {
  const typeValue = useFieldValue<CommentType>('type');

  const multipleVisible = typeValue === CommentType.Text;
  const timeVisible = typeValue === CommentType.Time;

  useHandleFieldModel(multipleVisible, timeVisible, typeValue);

  return (
    <div style={{ marginBottom: '16px', padding: '12px', border: '1px solid #e8e8e8', borderRadius: '4px' }}>
      <div style={{ display: 'flex', alignItems: 'center', marginBottom: '8px' }}>
        <SelectField
          options={CommentTypes}
          popupWidth={180}
          inline
        />
        <span style={{ margin: '0 8px' }}>留言标题为</span>
        <InputField inline {...inputProps} style={{ width: '150px' }} />
        <CheckboxField field="required" style={{ marginLeft: '8px' }}>
          必填
        </CheckboxField>
        {multipleVisible && (
          <CheckboxField field="multiple" style={{ marginLeft: '8px' }}>
            多行
          </CheckboxField>
        )}
        {timeVisible && (
          <CheckboxField field="includeDate" style={{ marginLeft: '8px' }}>
            含日期
          </CheckboxField>
        )}
        <div
          style={{
            marginLeft: '8px',
            color: '#1890ff',
            cursor: 'pointer',
            textDecoration: 'underline'
          }}
          onClick={onDelete}
        >
          删除
        </div>
      </div>
    </div>
  );
};

const Component = (props: IProps) => {
  const { onChange, value = [] } = props.input;
  const comments = useFieldArray<CommentData, any>('comments');

  // 同步外部value到内部fieldArray
  useEffect(() => {
    if (value.length !== comments.children.length) {
      // 清空现有数据
      comments.splice(0, comments.children.length);
      // 添加新数据
      value.forEach(item => {
        comments.push(item);
      });
    }
  }, [value, comments]);

  // 监听内部变化，同步到外部
  useEffect(() => {
    const currentValue = comments.children.map(child => child.getValue());
    if (JSON.stringify(currentValue) !== JSON.stringify(value)) {
      onChange(currentValue);
    }
  }, [comments.children, onChange, value]);

  return (
    <div>
      {comments.children.map((childModel, index) => (
        <FieldSet key={childModel.id} model={childModel}>
          <CommentItem
            inputProps={{
              maxLength: MaxTitleLength
            }}
            onDelete={() => comments.splice(index, 1)}
          />
        </FieldSet>
      ))}
      {comments.children.length < MaxCommentsLength && (
        <div
          style={{
            color: '#1890ff',
            cursor: 'pointer',
            textDecoration: 'underline',
            marginTop: '8px'
          }}
          onClick={() =>
            comments.children.length <= MaxCommentsLength &&
            comments.push({
              title: `留言 ${comments.children.length + 1}`,
              type: CommentType.Text,
              multiple: false,
              required: false,
              includeDate: false
            })
          }
        >
          添加字段
        </div>
      )}
      <FormDescription style={{ marginTop: '16px' }}>
        购买商品时让买家输入留言，最多可设置 {MaxCommentsLength} 条留言。
      </FormDescription>
    </div>
  );
};

export default Component;
