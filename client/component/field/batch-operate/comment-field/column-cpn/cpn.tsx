import React, { useState } from 'react';
import { Button, Dialog } from 'zent';
import { CommentData } from '../types';
import BatchComponent from '../batch-cpn/cpn';

interface IProps {
  input: {
    value: CommentData[];
    onChange: (val: CommentData[]) => void;
  };
}

const Component = (props: IProps) => {
  const { onChange, value = [] } = props.input;
  const [visible, setVisible] = useState(false);
  const [tempValue, setTempValue] = useState<CommentData[]>(value);

  const handleOpenDialog = () => {
    setTempValue([...value]); // 复制当前值作为临时值
    setVisible(true);
  };

  const handleCloseDialog = () => {
    setVisible(false);
  };

  const handleConfirm = () => {
    onChange(tempValue);
    setVisible(false);
  };

  const handleTempChange = (val: CommentData[]) => {
    setTempValue(val);
  };

  const getDisplayText = () => {
    if (value.length === 0) {
      return '点击设置';
    }
    return `已设置 ${value.length} 条留言`;
  };

  return (
    <div>
      <Button type="link" onClick={handleOpenDialog} style={{ padding: 0 }}>
        {getDisplayText()}
      </Button>
      
      <Dialog
        visible={visible}
        title="商品留言"
        onClose={handleCloseDialog}
        style={{ width: '600px' }}
        footer={
          <div>
            <Button onClick={handleCloseDialog}>取消</Button>
            <Button type="primary" onClick={handleConfirm}>
              确定
            </Button>
          </div>
        }
      >
        <BatchComponent
          input={{
            value: tempValue,
            onChange: handleTempChange
          }}
        />
      </Dialog>
    </div>
  );
};

export default Component;
