/* --------------------------------- 批量发布商品 --------------------------------- */
export { default as BatchPublishGoodsField } from './batch-publish-goods';

/* ------------------------------- 商品库商品 field ------------------------------ */
export { default as GoodsCategoryField } from './goods-category';
export { default as LifeCycleSelectField } from './life-cycle-select';
export { default as DeliveryTypeField } from './delivery-type-select';
export { ProductionTimeField } from './production-time-field';
export { default as StorageGoodsStatusField } from './storage-goods-status';
export { default as SkuPatternField } from './storage-sku-pattern';
export { default as InputTaxRateField } from './input-tax-rate-field';
export { default as SalesTaxRateField } from './sales-tax-rate-field';
export { default as She<PERSON>LifeWarningField } from './shelf-life-warning-field';

/* ---------------------------------- 门店商品 ---------------------------------- */
export { default as OfflineGoodsStatusField } from './offline-goods-status';
export { default as DiscountSelectField } from './discount-select-field';
export { default as OfflineGroupSelectField } from './offline-goods-group';
export { default as OfflineBuyLimitField } from './offline-buy-limit-field';

export { default as SaleNumStartAddField } from './sale-num-start-add';

/* ---------------------------------- 网店商品 ---------------------------------- */
export { default as OnlineGoodsStatusField } from './online-goods-status';
export { default as OnlineGoodsGroup } from './online-goods-group';
export { default as GoodsTemplateSelectField } from './goods-template-select';
export { default as SaleTimeField } from './sale-time-field';
export { default as BuyLimitField } from './buy-limit-field';
export { default as IdLimitField } from './id-limit-field';
export { default as DeliveryTemplateField } from './delivery-template-field';
export { default as LogisticsTimelinessTemplateField } from './logistics-timeliness-template-field';
export { default as OriginPriceField } from './origin-price-field';
export { default as CategoryParam } from './category-param';
export { default as GoodsNoField } from './online-goods-no';
export { default as DistributionField } from './distribution-field';
export { default as AfterSaleField } from './online-after-sale';
export { default as SkuStockField } from './sku-stock-field';
export { default as SkuNoField } from './sku-no-field';
export { default as BarcodeField } from './online-barcode-field';
export { default as ShowStockField } from './show-stock-field';
export { default as PrepareTimeField } from './prepare-time-field';
export { default as SaleNumStartField } from './sale-num-start';
export { default as SkuWeightField } from './sku-weight-field';
export { default as SaleTypeField } from './online-sale-type';
export { default as ScheduleDisplayOffField } from './schedule-display-off-time';
export { default as GoodsClassificationSelectField } from './goods-classification';
export { default as GoodsBrandSelectField } from './goods-brand';
export { default as AttrSettingDialog } from './goods-attr';
export { default as HeavyContinuedField } from './heavy-continued-field';
export { default as SellingPointField } from './selling-point-field';
export { default as GoodsIndexField } from './goods-index';

/* ---------------------------------- 店内组织 ---------------------------------- */
export * from './shop-counter';

// 通用改名 Field
export { default as BatchRenameField } from './batch-rename';

export { default as ScheduleDisplayField } from './schedule-display-field';

/* ---------------------------------- 美团商品 ---------------------------------- */

export * from './external-group-field';

/* ---------------------------------- 饿了么商品 ---------------------------------- */

/* ---------------------------------- 美团闪购商品 ---------------------------------- */
