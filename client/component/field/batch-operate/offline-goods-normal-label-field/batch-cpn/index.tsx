import React from 'react';
import { Field } from '@youzan/retail-form';

import Component from './cpn';
import './index.scss';

interface IProps {
  name: string;
}

const OfflineGoodsNormalLabelBatch = (props: IProps) => {
  return (
    <div className='offline-goods-normal-label-batch'>
      <Field
        component={Component}
        label="商品普通标签："
        {...props}
      />
    </div>
  );
};

export default OfflineGoodsNormalLabelBatch;
