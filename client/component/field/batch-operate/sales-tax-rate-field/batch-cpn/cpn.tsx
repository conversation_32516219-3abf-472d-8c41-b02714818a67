import React from 'react';
import { TaxRateSelect } from '@youzan/biz-select-center';

interface IProps {
  input: {
    value: any;
    onChange: (val: any) => void;
  };
}

const Component = (props: IProps) => {
  const { onChange, value } = props.input;

  const handleChange = (val: string) => {
    onChange(val);
  };

  return <TaxRateSelect value={value} onChange={handleChange} placeholder="请选择销项税率" />;
};

export default Component;
