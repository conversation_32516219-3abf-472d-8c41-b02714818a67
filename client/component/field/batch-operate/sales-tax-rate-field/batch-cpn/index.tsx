import React from 'react';
import { Field } from '@youzan/retail-form';

import Component from './cpn';
import style from './index.scss';

interface IProps {
  name: string;
}

const SalesTaxRateBatch = (props: IProps) => {
  return (
    <div className={style.salesTaxRate}>
      <Field
        component={Component}
        label="销项税率："
        {...props}
        required
      />
    </div>
  );
};

export default SalesTaxRateBatch;
