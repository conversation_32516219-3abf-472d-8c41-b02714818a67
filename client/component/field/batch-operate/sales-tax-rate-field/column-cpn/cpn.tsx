import React from 'react';
import { TaxRateSelect } from '@youzan/biz-select-center';

interface IProps {
  input: {
    value: any;
    onChange: (val: any) => void;
  };
}

const Component = (props: IProps) => {
  const { value, onChange } = props.input;

  const handleChange = (val: string) => {
    onChange(val);
  };

  return <TaxRateSelect width={120} value={value} onChange={handleChange} placeholder="请选择" />;
};

export default Component;
