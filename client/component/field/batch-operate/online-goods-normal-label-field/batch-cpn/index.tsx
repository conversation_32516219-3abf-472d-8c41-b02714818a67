import React from 'react';
import { Field } from '@youzan/retail-form';

import Component from './cpn';
import './index.scss';

interface IProps {
  name: string;
}

const OnlineGoodsNormalLabelBatch = (props: IProps) => {
  return (
    <div className='online-goods-normal-label-batch'>
      <Field
        component={Component}
        label="商品普通标签："
        {...props}
      />
    </div>
  );
};

export default OnlineGoodsNormalLabelBatch;
