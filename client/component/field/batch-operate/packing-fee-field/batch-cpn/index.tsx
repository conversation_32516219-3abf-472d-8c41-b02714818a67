import React from 'react';
import { Field } from '@youzan/retail-form';

import Component from './cpn';
import './index.scss';

interface IProps {
  name: string;
}

const PackingFeeBatch = (props: IProps) => {
  return (
    <div className='packing-fee-batch'>
      <Field
        component={Component}
        label="打包费："
        {...props}
      />
    </div>
  );
};

export default PackingFeeBatch;
