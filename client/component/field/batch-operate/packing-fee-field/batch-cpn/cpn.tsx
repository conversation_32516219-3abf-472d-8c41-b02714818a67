import React from 'react';
import { Radio, NumberInput } from 'zent';
import './index.scss';

// 打包费配置枚举
export enum PackingPriceConfig {
  Unite = 0, // 统一配置
  Custom = 1 // 按规格自定义
}

const PackingPriceConfigTextMap = new Map([
  [PackingPriceConfig.Unite, '统一配置'],
  [PackingPriceConfig.Custom, '按规格自定义']
]);

interface IProps {
  input: {
    value: {
      config?: PackingPriceConfig;
      price?: string;
    };
    onChange: (val: any) => void;
  };
}

const Component = (props: IProps) => {
  const { onChange, value = {} } = props.input;
  const { config = PackingPriceConfig.Unite, price = '' } = value;

  const handleConfigChange = (e: any) => {
    const newConfig = Number(e.target.value) as PackingPriceConfig;
    onChange({
      ...value,
      config: newConfig,
      // 如果切换到按规格自定义，清空价格
      price: newConfig === PackingPriceConfig.Custom ? '' : price
    });
  };

  const handlePriceChange = (val: string) => {
    onChange({
      ...value,
      price: val
    });
  };

  return (
    <div className='packing-fee-container'>
      <Radio.Group value={config} onChange={handleConfigChange}>
        <div className="unite">
          <Radio value={PackingPriceConfig.Unite}>
            {PackingPriceConfigTextMap.get(PackingPriceConfig.Unite)}
          </Radio>
          {config === PackingPriceConfig.Unite && (
            <NumberInput
              addonBefore="¥"
              inline
              width={140}
              decimal={2}
              value={price}
              onChange={handlePriceChange}
              min={0}
              placeholder="请输入"
            />
          )}
        </div>
        <Radio value={PackingPriceConfig.Custom}>
          {PackingPriceConfigTextMap.get(PackingPriceConfig.Custom)}
        </Radio>
      </Radio.Group>
    </div>
  );
};

export default Component;
