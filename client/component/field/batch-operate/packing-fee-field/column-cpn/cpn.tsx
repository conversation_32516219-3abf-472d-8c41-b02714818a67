import React, { useEffect, useRef } from 'react';
import { Radio, NumberInput } from 'zent';

// 打包费配置枚举
export enum PackingPriceConfig {
  Unite = 0, // 统一配置
  Custom = 1 // 按规格自定义
}

const PackingPriceConfigTextMap = new Map([
  [PackingPriceConfig.Unite, '统一配置'],
  [PackingPriceConfig.Custom, '按规格自定义']
]);

interface IProps {
  input: {
    value: {
      config?: PackingPriceConfig;
      price?: string;
    };
    onChange: (val: any) => void;
  };
  goodsData?: any; // 商品数据，用于判断是否多sku
}

const Component = (props: IProps) => {
  const { onChange, value = {} } = props.input;
  const { config = PackingPriceConfig.Unite, price = '' } = value;
  const prevValueRef = useRef(value);

  // 判断是否是多sku商品
  const isMultiSku = !props.goodsData?.isNonSpec;

  // 监听value变化，如果是批量操作导致的变化且选择了按规格自定义，需要判断是否应该应用
  useEffect(() => {
    const prevValue = prevValueRef.current;
    const currentValue = value;

    // 如果值发生了变化，且新值是按规格自定义，但当前商品是单sku
    if (
      prevValue !== currentValue &&
      currentValue?.config === PackingPriceConfig.Custom &&
      !isMultiSku
    ) {
      // 恢复到之前的值（不应用批量修改）
      onChange(prevValue);
      return;
    }

    // 更新引用
    prevValueRef.current = currentValue;
  }, [value, isMultiSku, onChange]);

  const handleConfigChange = (e: any) => {
    const newConfig = Number(e.target.value) as PackingPriceConfig;

    // 如果选择按规格自定义但当前商品是单sku，不允许切换
    if (newConfig === PackingPriceConfig.Custom && !isMultiSku) {
      return;
    }

    onChange({
      ...value,
      config: newConfig,
      // 如果切换到按规格自定义，清空价格
      price: newConfig === PackingPriceConfig.Custom ? '' : price
    });
  };

  const handlePriceChange = (val: string) => {
    onChange({
      ...value,
      price: val
    });
  };

  return (
    <div>
      <Radio.Group value={config} onChange={handleConfigChange}>
        <div style={{ display: 'flex', alignItems: 'center', marginBottom: '8px' }}>
          <Radio value={PackingPriceConfig.Unite}>
            {PackingPriceConfigTextMap.get(PackingPriceConfig.Unite)}
          </Radio>
          {config === PackingPriceConfig.Unite && (
            <NumberInput
              addonBefore="¥"
              inline
              width={150}
              decimal={2}
              value={price}
              onChange={handlePriceChange}
              min={0}
              placeholder="请输入"
              style={{ marginLeft: '4px' }}
            />
          )}
        </div>
        <Radio value={PackingPriceConfig.Custom} disabled={!isMultiSku}>
          {PackingPriceConfigTextMap.get(PackingPriceConfig.Custom)}
        </Radio>
      </Radio.Group>
    </div>
  );
};

export default Component;
