/* eslint-disable @youzan/yz-retail/prefer-pascal-case-const */
/* eslint-disable @typescript-eslint/no-non-null-assertion */
/* eslint-disable @youzan/yz-retail/typescript/prefer-pascal-case-enums */
import * as React from 'react';
import {
  Dropdown,
  DropdownClickTrigger,
  DropdownContent,
  DropdownButton,
  Menu,
  MenuItem,
  DropdownPosition,
  Pop,
  Icon
} from 'zent';

export enum BatchModifySelectEnum {
  MODIFY_SELECT_GOODS = '1',
  MODIFY_ALL_GOODS = '2'
}

const MAX_MODIFY_NUM = 5000;

export const ListBatchContext = React.createContext(null);

export const useBatchTableSelect = () => {
  const [currentModify, setCurrentModifyInside] = React.useState<BatchModifySelectEnum>(
    BatchModifySelectEnum.MODIFY_SELECT_GOODS
  );
  const inverseSelectData = React.useRef([]);
  const setCurrentModify = (key: BatchModifySelectEnum) => {
    setCurrentModifyInside(key);
  };
  const setInverseSelectData = (data: any[]) => {
    inverseSelectData.current = data;
  };

  React.useEffect(() => {
    inverseSelectData.current = [];
  }, [currentModify]);

  return {
    currentModify,
    setCurrentModify,
    inverseSelectData,
    setInverseSelectData
  };
};

function renderOptionContent(item: any, modifyAllGoodsDisabled: boolean) {
  const { key, text } = item;

  return (
    <span>
      <span>{text}</span>
      <span>
        {/* 修改全部商品展示帮助信息 */}
        {key === BatchModifySelectEnum.MODIFY_ALL_GOODS && modifyAllGoodsDisabled && (
          <Pop
            trigger="hover"
            centerArrow
            position="right-center"
            content={<div>筛选结果商品数量不能大于{MAX_MODIFY_NUM}</div>}
          >
            <Icon type="help-circle" />
          </Pop>
        )}
      </span>
    </span>
  );
}

const TableBatchWaySelect: React.FC = () => {
  const [visible, setVisible] = React.useState(false);
  const [current, setCurrent] = React.useState(BatchModifySelectEnum.MODIFY_SELECT_GOODS);
  const ctxValue = React.useContext(ListBatchContext) || {};
  const modifyAllGoodsDisabled = ctxValue.total > MAX_MODIFY_NUM;

  const batchModifyOptions = [
    {
      key: BatchModifySelectEnum.MODIFY_SELECT_GOODS,
      text: '当页全选'
    },
    {
      key: BatchModifySelectEnum.MODIFY_ALL_GOODS,
      text: '全部全选',
      disabled: modifyAllGoodsDisabled
    }
  ];

  const handleChange = (_: any, key: BatchModifySelectEnum) => {
    setVisible(false);
    setCurrent(key);
    if (typeof ctxValue.onModifyWayChange === 'function') {
      ctxValue.onModifyWayChange(key);
    }
  };

  const currentText = batchModifyOptions.find(option => option.key === current)!.text;

  return (
    <Dropdown
      containerSelector="body"
      cushion={0}
      visible={visible}
      onVisibleChange={v => setVisible(v)}
      position={DropdownPosition.AutoBottomLeft}
    >
      <DropdownClickTrigger>
        <DropdownButton>{currentText}</DropdownButton>
      </DropdownClickTrigger>
      <DropdownContent>
        <Menu onClick={handleChange}>
          {batchModifyOptions.map(item => (
            <MenuItem key={item.key} disabled={item.disabled}>
              {renderOptionContent(item, modifyAllGoodsDisabled)}
            </MenuItem>
          ))}
        </Menu>
      </DropdownContent>
    </Dropdown>
  );
};

export default TableBatchWaySelect;
